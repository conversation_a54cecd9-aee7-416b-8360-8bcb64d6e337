"use client";

import Logo from "@/public/assets/images/logo.png";
import Image from "next/image";
import PhoneNumberForm from "./PhoneNumberForm";
import VerifyCodeForm from "./VerifyCodeForm";
import CustomButton from "../UI/CustomButton";
import { useEffect, useState } from "react";

import Card from "@/components/common/Card";
import { useForm } from "react-hook-form";
import { loginFormSchema, LoginFormType } from "@/lib/types/zod-schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { authOTP, authVerification } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { LOGIN_CODE_LENGTH, RETURN_URL } from "@/lib/constants";
import { Form } from "@/components/UI/form";
import CountDownTimer from "@/components/common/CountDownTimer";
import { useRouter, useSearchParams } from "next/navigation";
import { CircleLoader } from "react-spinners";
import { cn } from "@/lib/utils";
import { useAuth } from "@/lib/hooks/useAuth";
import Container from "@/components/common/Container";
import { HOME_PATH } from "@/lib/routes";
import BackLink from "@/components/common/BackLink";
import usePathUrlHelper from "@/lib/hooks/usePathUrlHelper";
import useInquiryPost from "@/lib/hooks/useInquiryPost";
import { ViolationQueryParams } from "@/lib/types/types";
import { InquiryInfo } from "@/components/common/InquiryInfo";
import {
    getCarIdDocumentsInquiry,
    getCarInsuranceInquiry,
    getDrivingLicensePoint,
    getDrivingStatusInquiry,
} from "@/actions/inquiry.action";
import { CarInsuranceData, getCarIdDocumentsData, getDrivingStatusData } from "@/lib/types/action-types";
import { getCarIdDocumentsInquiryLogin } from "@/actions/other.action";

type FormStep = 1 | 2;
const LOGIN_COUNTER_KEY = "loginCounter";

const LoginBox = () => {
    const [formStep, setFormStep] = useState<FormStep>(1);
    const [enterCodeLoading, setEnterCodeLoading] = useState(false);
    const { getQueryParams } = usePathUrlHelper();
    // this state has ony defined to use car and motor tickets inquiries
    const [userInquiryInfo, setUserInquiryInfo] =
        useState<ViolationQueryParams>();
    // this one will be dynamic to use in any other inquiries
    const [inquiryParams, setInquiryParams] = useState<ViolationQueryParams>();
    const { mutate } = useInquiryPost();
    const [tempToken, setTempToken] = useState<string | undefined>();
    const [optLoading, setOptLoading] = useState(false);
    const [expireTime, setExpireTime] = useState<number | undefined>();
    const router = useRouter();
    const searchParam = useSearchParams();
    const { reFetchUser } = useAuth();

    const form = useForm<LoginFormType>({
        resolver: zodResolver(loginFormSchema),
        defaultValues: {
            mobile: "",
            code: "",
        },
    });

    useEffect(() => {
        const queryParams = getQueryParams<ViolationQueryParams>();
        console.log(queryParams);
        //these mother fuckers need to show plate and plaque
        if (
            queryParams.inquiry === "true" ||
            queryParams.type == "carid_documents" ||
            queryParams.type == "license_status" ||
            queryParams.type == "car_insurance"
        ) {
            setUserInquiryInfo(queryParams);
        }
        //these services wont need plate to show while user logins
        if (queryParams.type === "license_point") {
            setInquiryParams(queryParams);
        }
    }, []);

    async function onSubmit(values: LoginFormType) {
        const queryParams = getQueryParams<ViolationQueryParams>();

        if (enterCodeLoading) return;
        setEnterCodeLoading(true);
        const phone = values.mobile;
        const code = values.code;
        const verificationResult = await authVerification({
            phone,
            code,
            token: tempToken!,
        });
        if (!verificationResult.success) {
            setEnterCodeLoading(false);
            toast.error(verificationResult.message!);
            return;
        }
        

        reFetchUser();
        if (userInquiryInfo && queryParams.inquiry === "true") {
            const mutateResult = await mutate(userInquiryInfo);
            if (!mutateResult.success && mutateResult.href) {
                router.replace(mutateResult.href);
            } else if (!mutateResult.success && mutateResult.message) {
                toast.error(mutateResult.message);
                router.back();
            } else if (mutateResult.success && mutateResult.href) {
                router.replace(mutateResult.href);
            }
        } else if (inquiryParams && inquiryParams.type === "license_point") {
            const apiData = {
                national_id: inquiryParams.national_id || "",
                license_number: inquiryParams.license_number || "",
                mobile_number: inquiryParams.mobile_number || "",
            };

            const response = await getDrivingLicensePoint(apiData);
            if (response.success) {
                router.replace(
                    `/driving-license-point/result?trace_number=${response.data.traceNumber}`
                );
            } else {
                toast.error(response.message!);
            }
        } else if (userInquiryInfo && queryParams.type === "carid_documents") {
            const data: getCarIdDocumentsData = {
                type: userInquiryInfo.isMotor === "true" ? "motor" : "car",
                details: {
                    national_id: userInquiryInfo.national_id ?? "",
                    phone: userInquiryInfo.mobile_number ?? "",
                },
                plaque: {
                    left: userInquiryInfo.left ?? "",
                    mid: userInquiryInfo.middle ?? "",
                    right: userInquiryInfo.right ?? "",
                    alphabet: String(userInquiryInfo.alphabet) ?? "",
                },
            };

            const response = await getCarIdDocumentsInquiry(data);
            // debugger
            // console.log("API response:", response);

            if (response?.success) {
                return router.replace(
                    `/carid-documents/result?trace_number=${response.data.traceNumber}`
                );
            } else if (response.status === 402) {

                // TODO: Show error message to user
                localStorage.setItem("walletMessage", response.data.message);
                return router.push(
                    `/wallet?left=${data.plaque.left}&alphabet=${data.plaque.alphabet}&middle=${data.plaque.mid}&right=${data.plaque.right}&national_id=${data.details.national_id}&mobile_number=${data.details.phone}`
                );
            } else {
                router.push("/carid-documents")
                toast.error(response?.data?.message || "خطایی رخ داده است", {duration: 10000});
            }
        }
        else if (userInquiryInfo && queryParams.type === "license_status") {
            const data: getDrivingStatusData = {
                national_id: userInquiryInfo.national_id ?? "",
                phone: userInquiryInfo.mobile_number ?? "",
            };

            const response = await getDrivingStatusInquiry(data);
            // console.log("API response:", response);

            if (response?.success) {
                return router.replace(
                    `/driving-license-status/result?trace_number=${response.data.traceNumber}`
                );
            } else if (response.status === 402) {

                // TODO: Show error message to user
                localStorage.setItem("walletMessage", response.data.message);
                return router.push(
                    `/wallet?national_id=${data.national_id}&mobile_number=${data.phone}`
                );
            } else {
                router.push("/driving-license-status")
                toast.error(response?.data?.message || "خطایی رخ داده است", {duration: 10000});
            }
        } else if (userInquiryInfo && queryParams.type === "car_insurance") {
            const data: CarInsuranceData = {
                details: {
                    national_id: userInquiryInfo.national_id ?? "",
                    unique_insurance_policy: userInquiryInfo.unique_insurance_policy ?? "",
                },
                plaque: {
                    left: userInquiryInfo.left ?? "",
                    mid: userInquiryInfo.middle ?? "",
                    right: userInquiryInfo.right ?? "",
                    alphabet: String(userInquiryInfo.alphabet) ?? "",
                },
            };

            const response = await getCarInsuranceInquiry(data);
            // console.log("API response:", response);

            if (response?.success) {
                return router.replace(
                    `/car-insurance/result?trace_number=${response.data.traceNumber}`
                );
            } else if (response.status === 402) {

                // TODO: Show error message to user
                localStorage.setItem("walletMessage", response.data.message);
                return router.push(
                    `/wallet?national_id=${data.details.national_id}&mobile_number=${data.details.unique_insurance_policy}`
                );
            } else {
                router.push("/car-insurance")
                toast.error(response?.data?.message || "خطایی رخ داده است", {duration: 10000});
            }
        }

        else {
            const returnUrl = searchParam.get(RETURN_URL);
            const url = returnUrl || HOME_PATH;
            router.replace(url);
        }

        setEnterCodeLoading(false);
        toast.success("ورود با موفقیت");
        await reFetchUser();
    }

    function lockUser() {
        const currentTime = new Date();
        const loginCounterInfo = localStorage.getItem(LOGIN_COUNTER_KEY);
        let prevMobile: string | undefined;
        let counter = 0;
        if (loginCounterInfo) {
            const separatedCounterInfo = loginCounterInfo.split("_");
            prevMobile = separatedCounterInfo[0];
            counter = parseInt(separatedCounterInfo[1]);
            const prevTime = new Date(separatedCounterInfo[2]);
            const diffInMilliseconds = Math.abs(
                currentTime.getTime() - prevTime.getTime()
            );
            const fiveMinutesInMilliseconds = 5 * 60 * 1000;
            const lessThanFiveMin = diffInMilliseconds <= fiveMinutesInMilliseconds;
            if (counter === 3 && lessThanFiveMin) {
                return true;
            }
            if (!lessThanFiveMin) {
                counter = 0;
                localStorage.removeItem(LOGIN_COUNTER_KEY);
            }
        }

        if (prevMobile !== form.getValues("mobile")) {
            counter += 1;
            const loginCounterValue = `${form.getValues(
                "mobile"
            )}_${counter}_${currentTime.toString()}`;
            localStorage.setItem(LOGIN_COUNTER_KEY, loginCounterValue);
        }
        return false;
    }

    async function authOTPAction() {
        if (optLoading) return;
        if (!optLoading) {
            setOptLoading(true);
        }

        if (lockUser()) {
            toast.error("به دلیل درخواست بالا لطفا مجددا 5 دقیفه ی دیگر تلاش کنید");
            setOptLoading(false);
            return;
        }
        const actionResult = await authOTP(form.getValues("mobile"));
        if (!actionResult.success) {
            toast.error(actionResult.message! || "");
            setOptLoading(false);
            return;
        }
        setTempToken(actionResult.data!.token);
        setExpireTime(actionResult.data!.expire_time); // change to milliseconds
        setOptLoading(false);
        setFormStep(2);
    }

    const codeLength = form.watch("code").length;

    async function formSubmitOnCodeEntered() {
        const isValid = await form.trigger();
        if (isValid) {
            await form.handleSubmit(onSubmit)();
        }
    }

    useEffect(() => {
        if (codeLength >= LOGIN_CODE_LENGTH) {
            formSubmitOnCodeEntered();
        }
    }, [codeLength]);

    async function handleGetCode() {
        setOptLoading(true);
        const mobileValid = await form.trigger("mobile");
        if (!mobileValid) {
            setOptLoading(false);
            return;
        }
        // const success = await handleRecaptcha();
        // if (success) {
        await authOTPAction();
        // }
        // setOptLoading(false)
    }

    async function onGetCodeButtonClick() {
        await handleGetCode();
    }

    function onBackClick(editMode = false) {
        form.reset({
            mobile: editMode ? form.getValues("mobile") : "",
            code: "",
        });
        setExpireTime(undefined);
        setTempToken(undefined);
        // recaptchaRef.current?.reset();
        setFormStep(1);
    }

    function onMobileInputKeyDown(e: React.KeyboardEvent<HTMLInputElement>) {
        if (e.key === "Enter") {
            e.preventDefault();
            handleGetCode();
        }
    }

    return (
        <Container center className="min-h-screen">
            <Card className="relative max-w-[450px] login-form min-h-[432px]">
                <div className="login-box-header">
                    <Image src={Logo} alt="login-logo" />
                </div>
                {userInquiryInfo && userInquiryInfo.left && <InquiryInfo data={userInquiryInfo} />}
                {formStep === 2 && <BackLink onClick={onBackClick} />}
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} autoComplete="off">
                        {formStep === 2 ? (
                            <VerifyCodeForm
                                form={form}
                                onEditClick={() => onBackClick(true)}
                            />
                        ) : (
                            <PhoneNumberForm
                                isInquiry={!!userInquiryInfo}
                                onMobileInputKeyDown={onMobileInputKeyDown}
                                form={form}
                            />
                        )}
                        <div className="mt-8">
                            {formStep === 2 ? (
                                <CustomButton
                                    key={1}
                                    type="submit"
                                    loading={enterCodeLoading}
                                    className="!py-5"
                                    disabled={enterCodeLoading}
                                >
                                    تایید
                                </CustomButton>
                            ) : (
                                <CustomButton
                                    key={2}
                                    type="button"
                                    loading={optLoading}
                                    className="!py-5"
                                    disabled={optLoading}
                                    onClick={onGetCodeButtonClick}
                                >
                                    {" "}
                                    ورود{" "}
                                </CustomButton>
                            )}
                        </div>
                        {formStep === 2 && (
                            <div className="flex justify-center items-center gap-1 items-cente mt-6">
                                <CountDownTimer
                                    remainingTime={expireTime}
                                    renderer={(second, minute) => {
                                        return (
                                            <div className="flex items-center text-sm gap-x-1">
                                                <span className="text-muted-foreground">
                                                    {String(minute).padStart(2, "0")}:
                                                    {String(second).padStart(2, "0")}
                                                </span>
                                                <span className="text-muted-foreground text-center">
                                                    مانده تا دریافت مجدد کد.
                                                </span>
                                            </div>
                                        );
                                    }}
                                    renderWhenCompleted={
                                        <p className="flex justify-center items-center text-sm gap-x-1 text-muted-foreground">
                                            <span>هنوز کد را دریافت نکرده اید؟</span>
                                            <span
                                                className={cn("text-primary", {
                                                    "cursor-pointer": !optLoading,
                                                })}
                                                onClick={authOTPAction}
                                            >
                                                ارسال دوباره
                                            </span>
                                            {optLoading && <CircleLoader color="blue" size={14} />}
                                        </p>
                                    }
                                />
                            </div>
                        )}
                    </form>
                </Form>
            </Card>
        </Container>
    );
};

export default LoginBox;
