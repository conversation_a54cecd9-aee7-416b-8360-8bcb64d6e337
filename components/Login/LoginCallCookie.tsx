"use client"
import { SetNaghlieCookie } from '@/actions/other.action'
import React, { useEffect, useState } from 'react'
import { useAuth } from '@/lib/hooks/useAuth'

interface LoginCallCookieProps {
  token?: string
  tokenType?: string
  expireAt?: number
  paymentLink?: string
}
const LoginCallCookie: React.FC<LoginCallCookieProps> = ({ token, tokenType, expireAt, paymentLink }) => {
  const { reFetchUser } = useAuth()
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
    async function setUserCookie() {
      console.log('LoginCallCookie: Starting authentication process');
      console.log('LoginCallCookie: Props received:', { token, tokenType, expireAt, paymentLink });

      try {
        if (token && tokenType) {
          console.log('LoginCallCookie: Token and tokenType are available, proceeding...');

          // Format the token properly (Bearer + access_token)
          const fullToken = `${tokenType} ${token}`;
          console.log('LoginCallCookie: Formatted token:', fullToken);

          await SetNaghlieCookie(fullToken)
          console.log('LoginCallCookie: Cookie set successfully');

          // Refresh user data after setting cookie
        //   await reFetchUser()
          console.log('LoginCallCookie: User data refreshed');

          // Redirect to payment link if available
          if (paymentLink) {
            console.log('LoginCallCookie: Redirecting to payment link:', paymentLink);
            window.location.href = "/";
            return; // Don't set isReady if redirecting
          } else {
            console.log('LoginCallCookie: No payment link provided');
          }
        } else {
          console.log('LoginCallCookie: Missing token or tokenType:', { token, tokenType });
        }
      } catch (error) {
        console.error('LoginCallCookie: Error setting authorization cookie:', error);
      } finally {
        if (!paymentLink) {
          setIsReady(true)
        }
      }
    }

    setUserCookie()
  }, [])

  if (!isReady) {
    return (
      <div className="flex justify-center items-center h-[90vh]">
      <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
    </div>
    )
  }

  return (
    <div className='hidden'></div>
  )
}

export default LoginCallCookie
