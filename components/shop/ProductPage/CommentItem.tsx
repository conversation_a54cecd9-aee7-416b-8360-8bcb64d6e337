import { Info, ThumbsUp, Thum<PERSON>Down, CircleUserRound, MessageCircle, ChevronDown, ChevronUp } from 'lucide-react'
import ProfilePic from "@/public/assets/images/profile-pic.png"
import Image from 'next/image'
import { ProductComment } from '@/lib/types/product.types'
import RatingStars from './RattingStars'
import ReplyItem from './ReplyItem'
import { useState } from 'react'

/**
 * CommentItem component for displaying individual product comments with replies
 * @param comment - The comment object containing comment data and replies
 * @returns JSX element representing a single comment with its replies
 */
const CommentItem = ({comment}: {comment: ProductComment}) => {
    console.log(comment);

    // State for toggling replies visibility
    const [showReplies, setShowReplies] = useState(true);

    /**
     * Format the date string to display in Persian format
     * @param dateString - ISO date string from API
     * @returns Formatted date string
     */
    const formatDate = (dateString: string) => {
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('fa-IR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch {
            return dateString;
        }
    };
    return (
        <div className='my-5'>
            <div className="bg-[#F5F6F8] rounded-2xl min-h-52 pb-5">
                <div className="commets-header flex justify-between md:w-[88%] max-md:w-full mx-auto pt-10 max-md:px-2">
                    <div className="right flex items-center md:gap-5 max-md:gap-2 md:w-[45%]">
                        <div className='max-w-[20%]'>
                            {/* <Image src={ProfilePic} alt="profile-pic" /> */}
                            <CircleUserRound size={40} />
                        </div>
                        <div className="flex flex-col">
                            <h3>
                                {comment.user.full_name}
                            </h3>
                            <div className="flex items-center text-sm max-md:text-xs h-7 gap-2">
                                <Info size={18} />
                                <span className="">
                                   {formatDate(comment.created_at)}
                                </span>
                                {comment.has_bought && (
                                    <>
                                        <span>|</span>
                                        <span className="text-green-600 text-xs">خریدار محصول</span>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                    <div className="left flex items-center gap-5 bg-gradient-to-l from-white to-transparent md:p-2.5 max-md: rounded-3xl">
                        <div className="flex items-center gap-5 border md:px-3 py-2 max-md:p-2 max-md:py-3 rounded-3xl text-gray-400">
                            <p className="flex items-center text-sm md:gap-2">
                                <span>
                                    0
                                </span>
                                <span>
                                    <ThumbsUp size={24} className=' max-md:w-5' />
                                </span>
                            </p>
                            <p className="flex items-center text-sm gap-2">
                                <span>
                                    0
                                </span>
                                <span>
                                    <ThumbsDown size={24} className=' max-md:w-5' />
                                </span>
                            </p>

                        </div>
                        <div className='review flex gap-2 items-center h-full my-2 max-md:hidden'>
                            <span className='text-sm'> {comment.rate} </span>
                            {/* <div className='flex gap-1 items-center h-full mr-2 max-md:hidden'>
                                <Star fill='#9DA5B0' className='w-4 text-[#9DA5B0]' />
                                <Star fill='#9DA5B0' className='w-4 text-[#9DA5B0]' />
                                <Star fill='#9DA5B0' className='w-4 text-[#9DA5B0]' />

                                <Star className='w-4 text-[#F7BC06]' fill='#F7BC06' /> <Star className='w-4 text-[#F7BC06]' fill='#F7BC06' />

                            </div> */}
                            <RatingStars rate={comment.rate} />

                        </div>

                    </div>

                </div>
                <div className="comments-body md:px-5 max-md:px-3 py-5 mb-2 mt-7 w-[94%] mx-auto bg-white rounded-3xl ">
                    <div className="mb-7">
                        <p className="leading-8 max-md:text-sm max-md:leading-7">
                                {comment.body}
                        </p>
                    </div>
                    {/* <div className="max-w-20 h-16 flex gap-4">
                        <Image className="rounded-xl" src={Motoroil} alt="product-img" />
                        <Image className="rounded-xl" src={Motoroil} alt="product-img" />
                        <Image className="rounded-xl" src={Motoroil} alt="product-img" />
                    </div> */}

                    {/* Replies Section */}
                    {comment.replies && comment.replies.length > 0 && (
                        <div className="replies-section mt-6 pt-4 border-t border-gray-200">
                            <div
                                className="flex items-center justify-between mb-4 cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors duration-200"
                                onClick={() => setShowReplies(!showReplies)}
                            >
                                <div className="flex items-center gap-2">
                                    <MessageCircle size={18} className="text-gray-500" />
                                    <span className="text-sm font-medium text-gray-600">
                                        پاسخ‌ها ({comment.replies.length})
                                    </span>
                                </div>

                                <div className="flex items-center gap-1 text-gray-500 hover:text-gray-700">
                                    <span className="text-xs max-md:hidden">
                                        {showReplies ? 'مخفی کردن' : 'نمایش'}
                                    </span>
                                    {showReplies ? (
                                        <ChevronUp size={16} />
                                    ) : (
                                        <ChevronDown size={16} />
                                    )}
                                </div>
                            </div>

                            {showReplies && (
                                <div className="space-y-4">
                                    {comment.replies.map((reply) => (
                                        <ReplyItem key={reply.id} reply={reply} />
                                    ))}
                                </div>
                            )}
                        </div>
                    )}
                </div>

            </div>
        </div>
    )
}

export default CommentItem