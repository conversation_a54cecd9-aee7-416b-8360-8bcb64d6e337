"use client";

import { Question } from "@/lib/types/product.types"
import dynamic from "next/dynamic";
const QuestionsAndAnswersList = dynamic(() => import('./QuestionsAndAnswersList'), {
    loading: () => <div className=''></div>,
    ssr: false,
  });

const QuestionsAndAnswers = ({ questions }: { questions: Question[] }) => {
    return (
        <QuestionsAndAnswersList questions={questions} />
    )
}

export default QuestionsAndAnswers