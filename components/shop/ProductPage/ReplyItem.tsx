import { UserRound } from 'lucide-react'
import { CommentReply } from '@/lib/types/product.types'

/**
 * ReplyItem component for displaying individual comment replies
 * @param reply - The reply object containing reply data
 */
const ReplyItem = ({ reply }: { reply: CommentReply }) => {
   

    return (
        <div className="reply-item bg-[#637382] text-white p-4 rounded-2xl transition-all duration-200 hover:bg-[#5a6570]">
            <div className="reply-content">
                <p className="leading-7 max-md:text-sm mb-3">
                    {reply.body}
                </p>

                <div className="reply-meta flex flex-wrap items-center gap-3 text-sm opacity-90">
                    <div className="flex items-center gap-2">
                        <UserRound size={16} />
                        <span> {reply.user.full_name || 'مدیر سایت'} </span>
                    </div>
                    <span className="hidden sm:inline">|</span>
                    <span>{reply.created_at}</span>

                    {/* Display rating if available */}
                    {/* {reply.rate && (
                        <>
                            <span className="hidden sm:inline">|</span>
                            <div className="flex items-center gap-1">
                                <span>امتیاز: {reply.rate}</span>
                            </div>
                        </>
                    )} */}

                    {/* Display purchase status if available */}
                    {reply.has_bought && (
                        <>
                            <span className="hidden sm:inline">|</span>
                            <span className="text-green-300">خریدار محصول</span>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ReplyItem;
