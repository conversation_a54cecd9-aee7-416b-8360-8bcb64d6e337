'use client';

import { useState, useEffect } from 'react';
import type { Swiper as SwiperType } from 'swiper';
import { Gallery } from '@/lib/types/product.types';

// Props interface for the SwiperWrapper
interface SwiperWrapperProps {
  images: Gallery[];
  thumbsSwiper: SwiperType | null;
  setThumbsSwiper: (swiper: SwiperType | null) => void;
  mainSwiperReady: boolean;
  setMainSwiperReady: (ready: boolean) => void;
  openModal: (index: number) => void;
  isModalOpen: boolean;
  modalStartIndex: number;
  modalThumbsSwiper: SwiperType | null;
  setModalThumbsSwiper: (swiper: SwiperType | null) => void;
  closeModal: () => void;
}

/**
 * Dynamic Swiper Component Loader
 * This component handles the dynamic loading of Swiper to reduce bundle size
 */
function DynamicSwiperLoader(props: SwiperWrapperProps) {
  const [SwiperComponent, setSwiperComponent] = useState<React.ComponentType<SwiperWrapperProps> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Dynamically import the SwiperWrapper component
    const loadSwiperWrapper = async () => {
      try {
        // Use dynamic import with proper typing
        const swiperModule = await import('./SwiperWrapper');
        setSwiperComponent(() => swiperModule.default);
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to load SwiperWrapper:', error);
        setIsLoading(false);
      }
    };

    loadSwiperWrapper();
  }, []);

  if (isLoading) {
    return (
      <div className="h-[400px] max-md:h-[250px] flex justify-center items-center">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!SwiperComponent) {
    return (
      <div className="h-[400px] max-md:h-[250px] flex justify-center items-center">
        <div className="text-gray-500">Failed to load gallery</div>
      </div>
    );
  }

  return <SwiperComponent {...props} />;
}


/**
 * ProductGallery component with dynamic Swiper loading
 * This component uses lazy loading to reduce initial bundle size
 */
export default function ProductGallery({ images }: { images: Gallery[] }) {
  const [thumbsSwiper, setThumbsSwiper] = useState<SwiperType | null>(null);
  const [mainSwiperReady, setMainSwiperReady] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalStartIndex, setModalStartIndex] = useState(0);
  const [modalThumbsSwiper, setModalThumbsSwiper] = useState<SwiperType | null>(null);

  /**
   * Opens the modal gallery at a specific image index
   * @param startIndex - The index of the image to start with
   */
  const openModal = (startIndex: number) => {
    setModalStartIndex(startIndex);
    setIsModalOpen(true);
  };

  /**
   * Closes the modal gallery and resets modal state
   */
  const closeModal = () => {
    setIsModalOpen(false);
    setModalThumbsSwiper(null);
  };

  return (
    <div className="md:p-6">
      <div className="relative rounded-2xl bg-[#F9FAFB]">
        {/* Discount badge */}
        {/* <div className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full z-10">
          10%
        </div> */}

        {/* Dynamically loaded Swiper component */}
        <DynamicSwiperLoader
          images={images}
          thumbsSwiper={thumbsSwiper}
          setThumbsSwiper={setThumbsSwiper}
          mainSwiperReady={mainSwiperReady}
          setMainSwiperReady={setMainSwiperReady}
          openModal={openModal}
          isModalOpen={isModalOpen}
          modalStartIndex={modalStartIndex}
          modalThumbsSwiper={modalThumbsSwiper}
          setModalThumbsSwiper={setModalThumbsSwiper}
          closeModal={closeModal}
        />
      </div>
    </div>
  );
}
