import { ServiceStatusType } from '@/lib/types/types'
import React from 'react'
import Container from '../common/Container'
import { PageDescription } from '../common/PageDescription'
import CarIdDocumentsForm from './CarIdDocumentsForm'
interface CarIdDocumentsFormProps {
    title: string
    status: ServiceStatusType
    isMotor: boolean
}
const CarIdDocumentsFormWrapper = ({title, status, isMotor} : CarIdDocumentsFormProps) => {
  return (
    <Container className="mb-16">
          <div className="w-full max-w-[653px]">
            <PageDescription
              title={title}
              description="اطلاعات خود را وارد کرده تا از وضعیت پرداختی‌های خود مطلع شوید."
            />
    
        
            <CarIdDocumentsForm
              isMotor={isMotor}
              status={status}
            />
          </div>
        </Container>
  )
}

export default CarIdDocumentsFormWrapper