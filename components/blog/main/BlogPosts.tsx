"use client";
import Pagination from '@/components/common/Pagination';
import BlogSliderCard from '../BlogSliderCard'
import RoundedArrow from '@/components/common/svg/RoundedArrow'
import { ArticlesApiResponse } from '@/lib/types/types'
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getBlogPostsByPage } from '@/actions/blog.action';
import { useRouter } from 'nextjs-toploader/app';
import BlogPostSkeleton from './BlogPostSkeleton';

const BlogPosts = () => {
    const pathname = usePathname();
    const segments = pathname.split("/").filter(Boolean);
    const lastSegment = segments[segments.length - 1];
    const initialPage = /^\d+$/.test(lastSegment) ? parseInt(lastSegment) : 1;
    const router = useRouter()
    
    const [articleData, setArticleData] = useState<ArticlesApiResponse | null>(null);
    const [page, setPage] = useState(initialPage);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        const getArticles = async () => {
            setLoading(true);
            const response = await getBlogPostsByPage(page);
            if (response.success) {
                setArticleData(response);
            } else {
                setArticleData(null);
            }
            setLoading(false);
        };
        getArticles();
    }, [pathname]);

    return (
        <div className="mb-10">
            <div className="flex items-center justify-center mb-10">
                <h2 className="text-xl">آخرین و جدیدترین مقالات سایت</h2>
                <RoundedArrow />
            </div>

            <div className="blog-posts flex flex-col md:grid md:grid-cols-3 md:gap-6 mx-auto px-4 md:px-8">
                
                {loading &&
                  Array.from({ length: 6 }).map((_, index) => (
                    <BlogPostSkeleton key={index} />
                  ))
                }

                
                {!loading && articleData?.data && articleData.data.length > 0 &&
                  articleData.data.map((item, index: number) => (
                    <BlogSliderCard {...item} key={index} postInfo={true} />
                  ))
                }

                
                {!loading && (!articleData || articleData.data.length === 0) && (
                  <h2 className="col-span-3 text-center text-gray-500 py-10">
                    مطلبی یافت نشد
                  </h2>
                )}
            </div>

            <div className="mt-auto w-full overflow-x-hidden pt-6 self-center">
                <Pagination 
                  currentPage={page}
                  lastPage={articleData?.meta?.last_page || 0}
                  onPageChange={(page) => {
                      setPage(page)
                      router.push(`/blog/${page}`)
                  }}
                />
            </div>
        </div>
    );
};

export default BlogPosts;
