"use client";
import { FC } from "react";

const BlogPostSkeleton: FC = () => {
  return (
    <div className="relative w-full min-h-[22rem] mb-5 block mt-4 bg-white rounded-3xl shadow-lg animate-pulse">
      
      <div className="w-full aspect-[16/8] rounded-2xl bg-gray-200"></div>

     
      <div className="p-4 flex flex-col gap-y-3">
        
        <div className="flex gap-2 items-center">
          <div className="w-16 h-5 bg-gray-200 rounded"></div>
          <div className="w-10 h-5 bg-gray-200 rounded"></div>
        </div>

        
        <div className="w-3/4 h-5 bg-gray-200 rounded"></div>

       
        <div className="flex gap-5 items-center">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
            <div className="w-20 h-4 bg-gray-200 rounded"></div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
            <div className="w-16 h-4 bg-gray-200 rounded"></div>
          </div>
        </div>

        
        <div className="w-[100px] h-[2px] bg-gray-200 rounded"></div>

        
        <div className="flex flex-col gap-2">
          <div className="w-full h-3 bg-gray-200 rounded"></div>
          <div className="w-5/6 h-3 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );
};

export default BlogPostSkeleton;
