import React, { useState } from 'react'
import ChoiceWrapper from '../common/ChoiceWrapper';
import SubtractBorderIcon from '../common/svg/SubtractBorderIcon';
import SubtractIcon from '../common/svg/SubtractIcon';
import { cn } from '@/lib/utils';
import ResultDetailsUpIcon from '../common/svg/ResultDetailsUpIcon';
import { getViolationImageAction } from '@/actions/inquiry.action';
import { CameraIcon, IdCard } from 'lucide-react';
import toast from 'react-hot-toast';
import DialogModal from '../common/DialogModal';
import ResultDetailsLeftIcon from '../common/svg/ResultDetailsLeftIcon';
import CustomButton from '../UI/CustomButton';
import { LicenseStatusRecord } from '@/lib/types/qabzino-types';

interface Props {
    detail: LicenseStatusRecord
    isCollapse?: boolean;
    index: number
}

const LicenseStatusDetails = ({ detail, isCollapse = true, index }: Props) => {
    const [loading, setLoading] = useState(false);
    const [collapse, setCollapse] = useState(index === 0 ? false : isCollapse)

    return (
        <>
            {loading && (
                <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
                    <div className="w-10 h-10 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
                </div>
            )}

            <ChoiceWrapper
                backgroundColor='#F9FAFB'
                borderColor={collapse ? "#EEEEEE" : "#CCCACA"}
                className={collapse ? 'border-solid !border' : ''}
            >
                <div
                    onClick={() => setCollapse(!collapse)}
                    className='relative  w-full cursor-pointer'>
                    <div className={cn('w-full p-2 pt-5 pb-10 max-h-[460px] duration-200 transition-all relative', {
                        "max-h-[215px]": collapse
                    })}>
                        <div className='absolute bottom-[-7px] left-[-13px]'>
                            {collapse ? <SubtractBorderIcon width={100} height={100} /> :
                                <SubtractIcon width={100} height={100} />}
                        </div>
                        <div className='absolute bottom-[-13px] left-[22px] cursor-pointer'
                            onClick={() => setCollapse((prev) => !prev)}>
                            {
                                !collapse ? (<ResultDetailsUpIcon width={30} height={30} />) : (<ResultDetailsLeftIcon />)
                            }
                        </div>
                        <div className='w-full flex justify-between items-center gap-x-1'>
                            <span>
                                <p className='text-[#000000]'>{detail.FirstName} {detail.LastName}</p>
                            </span>
                            <IdCard color='#9DA5B0' height={40} width={40} />
                        </div>
                        {collapse && (
                            <div className='my-3 space-y-3'>

                                <div className='w-full flex justify-between items-center text-[#596068]'>
                                    <span className='text-xs'>کد ملی:</span>
                                    <span className='text-xs'>{detail.NationalID}</span>
                                </div>
                                <div className='w-full flex justify-between items-center text-[#596068]'>
                                    <span className='text-xs'>شماره گواهینامه:</span>
                                    <span className='text-xs'>{detail.Number}</span>
                                </div>
                                <div className='w-full flex justify-between items-center text-[#596068]'>
                                    <span className='text-xs'>وضعیت:</span>
                                    <span className='text-xs'>{detail.Status}</span>
                                </div>
                                <div className='w-full flex justify-between items-center text-[#596068]'>
                                    <span className='text-xs'>نوع گواهینامه:</span>
                                    <span className='text-xs'>{detail.Type}</span>
                                </div>
                               
                            </div>

                        )}
                        <div className={cn('mt-5 flex flex-col items-center gap-5', {
                            "hidden": collapse,
                        })}>
                            <div className='w-full flex justify-between items-center text-[#596068] text-xs'>
                                <span>بارکد:</span>
                                <span>{detail.Barcode}</span>
                            </div>
                            {/*<div className='w-full flex justify-between items-center text-[#596068]'>*/}
                            {/*    <span className='text-xs'>روش ثبت:</span>*/}
                            {/*    <span className='text-xs'>{detail.}</span>*/}
                            {/*</div>*/}
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>کد ملی:</span>
                                <span className='text-xs'>{detail.NationalID}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>شماره گواهینامه:</span>
                                <span className='text-xs'>{detail.Number}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>وضعیت:</span>
                                <span className='text-xs'>{detail.Status}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>نوع گواهینامه:</span>
                                <span className='text-xs'>{detail.Type}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>میزان اعتبار:</span>
                                <span className='text-xs'>{detail.ValidityPeriodInYears}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>تاریخ درخواست:</span>
                                <span className='text-xs'>{detail.RequestDateTime}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>تاریخ تایید چاپ:</span>
                                <span className='text-xs'>{detail.PrintConfirmDateTime}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>تاریخ صدور:</span>
                                <span className='text-xs'>{detail.DateTime}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>تاریخ چاپ گواهینامه:</span>
                                <span className='text-xs'>{detail.PrintLicenseDateTime}</span>
                            </div>
                        </div>

                    </div>

                </div>
            </ChoiceWrapper>


        </>
    );
}

export default LicenseStatusDetails