import React from 'react'
import Container from '../common/Container'
import { PageDescription } from '../common/PageDescription'
import { DrivingLicenseStatusForm } from './DrivingLicenseStatusForm'
import { ServiceStatusType } from '@/lib/types/types'

type Props = {
    status?: ServiceStatusType
    title: string
}

const DrivingLicenseStatusWrapper = ({title,status = 'ACTIVE'}: Props) => {
  return (
    <Container>
                <div className='w-full max-w-[553px]'>
                    <PageDescription
                        title={title}
                        description='شماره تلفن و کد ملی خود را وارد کرده تا از وضعیت گواهینامه خود مطلع شوید.'
                    />
                    <DrivingLicenseStatusForm status={status}/>
                </div>
    </Container>
  )
}

export default DrivingLicenseStatusWrapper