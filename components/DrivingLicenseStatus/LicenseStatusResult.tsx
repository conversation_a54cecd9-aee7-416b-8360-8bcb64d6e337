"use client"
import { getDrivingLicenseStatusByTrace } from "@/actions/inquiry.action"
import { LicenseListResponse, LicenseStatusRecord } from "@/lib/types/qabzino-types"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import ChoiceWrapper from "../common/ChoiceWrapper"
import LicenseStatusDetails from "./LicenseStatusDetails"

const LicenseStatusResult = () => {
    const [inquiryData, setInquiryData] = useState<LicenseStatusRecord[] | null>(null)
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null)
    const params = useSearchParams()
    


    useEffect(() => {
        const traceNumber = params.get('trace_number') || '';

        const fetchData = async () => {
            if (traceNumber && !inquiryData) {
                // setLoading(true);
                const response: LicenseListResponse = await getDrivingLicenseStatusByTrace(traceNumber);
                console.log("response:", response);
                
                if (response?.success && response?.data?.result) {
                    setInquiryData(response?.data?.result);
                } else {
                    setError(response?.data?.message || "خطایی رخ داده است");
                }
                setLoading(false);
            }
        };

        fetchData();
    }, []);



    // TODO: this section must become a component
    const renderRow = (label: string, value?: string | number | boolean) => (
        <div className="flex justify-between border-b pb-6 border-gray-200">
            <span className="text-gray-500 text-base">{label}</span>
            <span className="font-medium">{value}</span>
        </div>
    );

    if (loading) {
        return (
            <div className="flex flex-col gap-6 animate-pulse">
                {[...Array(12)].map((_, i) => (
                    <div key={i} className="flex justify-between border-b pb-6 border-gray-200">
                        <div className="bg-gray-200 h-4 w-32 rounded" />
                        <div className="bg-gray-200 h-4 w-16 rounded" />
                    </div>
                ))}
            </div>
        );
    }

    return (
        <>
            {error ? <h2 className="text-red-500 text-lg text-center py-5">{error}</h2>
            :
            <div className="flex flex-col gap-6">
                {
                    inquiryData?.map((item, index) => (
                        <LicenseStatusDetails key={index} detail={item} index={index} />
                    ))
                }
                
            </div>                        
            }
        </>
    );
};

export default LicenseStatusResult;
