"use client";

import React from "react";
import "./Checkbox.css"; 

type Props = {
  id: string;
  label: string;
  checked?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

const Checkbox: React.FC<Props> = ({ id, label, checked, onChange }) => {
  return (
    <div className="checkbox-wrapper-15">
      <input
        className="inp-cbx"
        id={id}
        type="checkbox"
        checked={checked}
        onChange={onChange}
      />
      <label className="cbx flex gap-3" htmlFor={id}>
        <span>
          <svg width="12px" height="9px" viewBox="0 0 12 9">
            <polyline points="1 5 4 8 11 1"></polyline>
          </svg>
        </span>
        <p>{label}</p>
      </label>
    </div>
  );
};

export default Checkbox;
