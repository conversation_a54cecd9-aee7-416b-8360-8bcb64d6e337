import { FC } from "react";
import { SlidersHorizontal } from "lucide-react";

interface SortOption {
  label: string;
}

const sortOptions: SortOption[] = [
  { label: "همه" },
  { label: "ارزان ترین" },
//   { label: "پرفروش ترین" },
  { label: "گران ترین" },
  { label: "بیشترین توانایی" },
  { label: "بیشترین تعداد شعب" },
];

const Sortbar: FC = () => {
  return (
    <div className="bg-white flex-1 max-md:max-w-full max-md:px-3  py-8 w-full md:col-span-8 mr-auto gap-3 flex items-center px-5 rounded-3xl relative">
      {/* Desktop Label */}
      <div className="hidden md:flex gap-1 text-black items-center whitespace-nowrap md:text-base text-xs font-bold">
        <SlidersHorizontal size={16} />
        مرتب سازی:
      </div>

      {/* Desktop Sort Options */}
      <ul className="hidden md:flex flex-wrap gap-0 lg:gap-x-1 md:text-base text-sm">
        {sortOptions.map((option) => (
          <li
            key={option.label}
            className="cursor-pointer text-[#5E646B] py-2 px-3 whitespace-nowrap md:text-base text-xs font-light rounded-3xl"
          >
            {option.label}
          </li>
        ))}
      </ul>

      {/* Mobile Button */}
      <div className="md:hidden max-w-fit relative w-full">
        <button className="w-full max-md:py-1 md:px-2 py-2 rounded-3xl gap-x-[4px] flex justify-between items-center text-sm">
          <SlidersHorizontal size={16} />
          <span className="max-md:text-xs whitespace-nowrap">مرتب سازی</span>
        </button>
      </div>

      {/* Product Count */}
      <div className="bg-gradient-to-l max-lg:hidden absolute top-1/2 transform -translate-y-1/2 w-[150px] left-0 from-gray-100 to-transparent mr-20 py-3 rounded-3xl">
        <div className="flex justify-center items-center w-full">
          <span className="text-gray-800 text-sm">(31)</span>
          <span className="text-[#9DA5B0] text-sm mr-1">بیمه</span>
        </div>
      </div>
    </div>
  );
};

export default Sortbar;
