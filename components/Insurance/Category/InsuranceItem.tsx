import SpecialPrice from '@/components/common/SpecialPrice'
import { <PERSON><PERSON><PERSON>, Pin, Star } from 'lucide-react'
import Image from 'next/image'
import Iran from "@/public/assets/images/IRAN.webp"

const InsuranceItem = () => {
    return (
        <div className="flex justify-between bg-white p-5 rounded-3xl">
            <div className="w-[15%] relative p-5 border-2 border-dashed rounded-2xl">
                <Image src={Iran} alt='iran' className="w-full" width={300} height={300} />
            </div>
            <div className="w-[50%]">
                <div className="flex pb-3 justify-between items-center">
                    <h2 className="title-border relative">
                        بیمه ایران
                    </h2>
                    <div className="flex items-center gap-3 text-sm font-light">
                        <span className="bg-[#E0F3E6] px-2 py-1 text-[#2DC058] rounded-3xl">
                            امک<PERSON> خرید قسطی
                        </span>
                        <span className="bg-[#FFF7DF] px-2 py-1 text-[#F7BC06] rounded-3xl">
                            بدون پیش پرداخت
                        </span>
                    </div>
                </div>
                <div className="font-light mt-3">
                    <p className="flex items-center gap-2">
                        <CreditCard color="#9da5b0" size={18} />   امکان پرداخت خسارت سیار (تمامی شهر ها)
                    </p>
                    <div className="flex gap-5 mt-3">
                        <p className="flex items-center gap-2">
                            <Pin color="#9da5b0" size={18} />  <span className="font-bold">58</span> شعبه
                        </p>
                        <p className="flex items-center gap-2">
                            <Star size={18} fill='#F7BC06' color='#F7BC06' />  3 از 5 تعهد در پرداخت
                        </p>
                    </div>
                </div>
            </div>
            <div className="w-[28%] bg-gray-50 rounded-3xl p-3 flex justify-center items-center flex-col gap-3">
                <div className="flex gap-1 items-center">
                    <SpecialPrice price={798545646} className="text-sm" />
                    <span className="bg-red-500 text-white p-[2px] text-xs rounded-bl-full rounded-tl-full rounded-tr-full ">10%</span>
                </div>
                <div className="flex justify-between w-full items-center">
                    <span>مبلغ نهایی: </span>
                    <p>
                        <span>8,029,000</span> تومان
                    </p>
                </div>
                <button className="w-full bg-primary text-white p-3 py-2 rounded-xl  font-light">خرید بیمه</button>
            </div>
        </div>
    )
}

export default InsuranceItem