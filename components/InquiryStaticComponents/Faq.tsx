import {ReactNode} from "react";
import CustomAccordion from "../UI/CustomAccordion";

export interface FAQItem {
    question: string;
    answer: string ;
}


export interface FAQProps {
    faqs: FAQItem[]
    className?: string
    children?: ReactNode

}

const Faq = ({faqs, className, children}: FAQProps) => {

    return (
        <section className={`container mx-auto max-w-7xl ${className}`}>
            <div className=' mx-auto p-3 text-center pb-6'>
                <h2 className='text-xl md:text-2xl font-bold'>سوالات متداول </h2>
            </div>
            {children}
            <div className="container mx-auto max-w-7xl">
                <CustomAccordion faqs={faqs}/>
            </div>
        </section>
    )
}

export default Faq