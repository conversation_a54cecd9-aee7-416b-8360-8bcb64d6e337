'use client'

import React, { ReactNode, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { cn } from "@/lib/utils";
import { Accessibility, ChevronDown, X } from "lucide-react";
import { CAR_PLATE_ALPHABET, CAR_PLATE_LEFT, CAR_PLATE_MIDDLE, CAR_PLATE_RIGHT } from '@/lib/constants';
import PlateCarLeftIcon from "@/components/common/svg/PlateCarLeftIcon";
import PlateIranIcon from "@/components/common/svg/PlateIranIcon";
import PlateModalTitleIcon from "@/components/common/svg/PlateModalTitleIcon";
import { Dialog, DialogClose, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/UI/dialog";

type Props = {
    value?: string[],
    onChange?: (value: string[]) => void,
    readOnly?: boolean,
    ref?: React.Ref<PlateInputRef | null>,
}

export type PlateInputRef = {
    focus: () => void
}

type ReadOnlyFieldProps = {
    value: string | ReactNode
}


function ReadOnlyField({ value }: ReadOnlyFieldProps) {
    return (
        <div
            className='w-full flex justify-center items-center left-direction !text-xl text-center px-0 h-full py-0'
        >
            <span>
                {value}
            </span>
        </div>
    )
}

const DISABLED = 'معلولین'
export const carPlatePartsMaxLengths = [2, 7, 3, 2];
export const plateAlphabets = ["الف", "ب", "پ", "ت", "ث", "ج", "چ", "ح", "خ", "د", "ذ", "ر", "ز", "ژ", "س", "ش", "ص", "ض", "ط", "ظ", "ع", "غ", "ف", "ق", "ک", "گ", "ل", "م", "ن", "و", "ه", "ی", DISABLED, "S", "D"];

export default function PlateInputCar({ onChange, readOnly, ref, value = ["", "", "", ""] }: Props) {
    const [isOpen, setIsOpen] = useState<boolean | undefined>();
    const inputRefs = [useRef<HTMLInputElement | null>(null), useRef<HTMLInputElement | null>(null), useRef<HTMLInputElement | null>(null), useRef<HTMLInputElement | null>(null)];

    useEffect(() => {
        if (isOpen === false) {
            inputRefs[CAR_PLATE_MIDDLE].current?.focus();
        }
    }, [isOpen]);


    const handleInputChange = (partIndex: number, newValue: string) => {
        const isNotValid = (partIndex === CAR_PLATE_LEFT && newValue.length > carPlatePartsMaxLengths[CAR_PLATE_LEFT]) ||
            (partIndex === CAR_PLATE_ALPHABET && !plateAlphabets.includes(newValue)) ||
            (partIndex === CAR_PLATE_MIDDLE && newValue.length > carPlatePartsMaxLengths[CAR_PLATE_MIDDLE]) ||
            (partIndex === CAR_PLATE_RIGHT && newValue.length > carPlatePartsMaxLengths[CAR_PLATE_RIGHT])
        if (isNotValid) {
            return;
        }
        const updatedParts = [...value];
        updatedParts[partIndex] = newValue;
        onChange?.(updatedParts);
    };

    const handleKeyUp = (e: React.KeyboardEvent<HTMLInputElement>, currentIndex: number) => {


        if (e.key === "Backspace" && e.currentTarget.value.length === 0 && currentIndex > CAR_PLATE_LEFT) {
            const prevIndex = currentIndex - 1;
            inputRefs[prevIndex].current?.focus();
        } else if (e.key !== "Backspace" && e.currentTarget.value.length >= carPlatePartsMaxLengths[currentIndex] && currentIndex < CAR_PLATE_RIGHT) {
            const nextIndex = currentIndex + 1;
            inputRefs[nextIndex].current?.focus();
            if (nextIndex === 1) {
                setIsOpen(true);
            }
        }
    };

    useImperativeHandle(ref, () => ({
        focus: () => {
            if (inputRefs[0].current) {
                inputRefs[0].current.focus(); // Focus the first input
            }
        },
    }));

    const handleSelectCharacter = (character: string) => {
        handleInputChange(1, character);
        setIsOpen(false);

        setTimeout(() => {
    inputRefs[CAR_PLATE_MIDDLE].current?.focus();
  }, 50);
    };

    const handleInput = (event: React.ChangeEvent<HTMLInputElement>) => {
        event.target.value = event.target.value.replace(/\D/g, "");
    };

    return (
        <div
            className={cn('w-full overflow-hidden bg-white rounded-lg border-2 h-[63px] border-l-0 border-[#000000]  flex items-center justify-between gap-1', {
                "bg-[#F5F6F8]": readOnly,

            })}
            style={{ direction: 'ltr' }}>
            <div
                className='w-[10%] flex justify-center bg-[#1D389A] border border-[#1D389A]  min-w-[35px] shrink-0 rounded-tl-xl rounded-bl-xl'>
                <PlateCarLeftIcon width={35} height={30} />
            </div>
            <div className='w-[20%] px-0 h-full py-0'>
                {readOnly ? (<ReadOnlyField value={value[CAR_PLATE_LEFT]} />) : <input
                    onInput={handleInput}
                    ref={(el) => {
                        inputRefs[CAR_PLATE_LEFT].current = el;
                    }}
                    value={value[CAR_PLATE_LEFT]}
                    onChange={(e) => handleInputChange(CAR_PLATE_LEFT, e.target.value)}
                    onKeyUp={(e) => handleKeyUp(e, CAR_PLATE_LEFT)}
                    maxLength={carPlatePartsMaxLengths[CAR_PLATE_LEFT]}
                    type='tel'
                    className="left-direction !text-xl borderless-input text-center w-full h-full"
                    inputMode="numeric"
                    placeholder=" - - "
                />}
            </div>
            <div className='w-[20%]'>
                <Dialog open={isOpen} onOpenChange={setIsOpen}>
                    {
                        readOnly ? (
                            <ReadOnlyField value={value[CAR_PLATE_ALPHABET] ? (value[CAR_PLATE_ALPHABET] === DISABLED ?
                                <Accessibility size={27} /> : value[CAR_PLATE_ALPHABET]) : ' - '} />) : (
                            <DialogTrigger asChild>
                                <div role="button"
                                    tabIndex={0}
                                    onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && setIsOpen(true)}
                                    className='flex items-center justify-center gap-x-1 cursor-pointer'>
                                    <ReadOnlyField
                                        value={value[CAR_PLATE_ALPHABET] ? (value[CAR_PLATE_ALPHABET] === DISABLED ?
                                            <Accessibility size={27} /> : value[CAR_PLATE_ALPHABET]) : (
                                            <div className='flex items-center gap-x-1'>
                                                <span>-</span>
                                                <ChevronDown size={12} className='text-gray-400' />
                                            </div>
                                        )} />

                                </div>
                            </DialogTrigger>
                        )
                    }

                    {
                        !readOnly &&
                        <DialogContent className='w-fit p-5 bg-[#FFFFFF] [&>button]:hidden borderless-input'
                          onCloseAutoFocus={(e) => e.preventDefault()} // جلوگیری از فوکوس مجدد روی Trigger

                        >
                            <DialogHeader className=''>
                                <DialogTitle hidden>
                                </DialogTitle>
                                <div
                                    className='w-full h-full flex items-center justify-between border-b  pb-4 border-[#F1F1F1]'>
                                    <div className='flex items-center gap-2'>
                                        <PlateModalTitleIcon width={20} height={20} />
                                        <h3 className='text-[#5E646B] text-sm font-semibold'>انتخاب حرف پلاک</h3>
                                    </div>
                                    <div
                                        className='absolute h-[50px] top-0 left-[10px] w-[40px] rounded-b-full bg-gradient-to-t from-[#F5F6F8] to-transparent'>
                                    </div>
                                    <DialogClose asChild>
                                        <div
                                            className='absolute top-[15px] left-[14px] h-[30px] w-[30px] rounded-full bg-white cursor-pointer'>
                                            <div className='w-full h-full flex items-center justify-center'>
                                                <X size={16} />
                                            </div>
                                        </div>
                                    </DialogClose>
                                </div>

                            </DialogHeader>
                            <div
                                className="relative bg-[#FFFFFF] rounded-2xl"
                            >
                                <div className='flex flex-col gap-2 '>
                                    <div
                                        className='flex flex-wrap  w-[300px]'>
                                        {plateAlphabets.map((char, index) => (
                                            <div className='w-[20%] py-1 flex justify-center items-center' key={index}>
                                                <p
                                                    onClick={() => handleSelectCharacter(char)}
                                                    className={cn("bg-[#F7F8F9] text-sm border font-bold text-gray-600 w-[45px] h-[45px] flex justify-center items-center hover:bg-primary/10 cursor-pointer rounded-xl", {
                                                        'bg-primary/10 border-primary/40': value[CAR_PLATE_ALPHABET] === char
                                                    })}>
                                                    {
                                                        char === DISABLED ?

                                                            <Accessibility className='tex-lg' /> :
                                                            <span>{char}</span>
                                                    }
                                                </p>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                            </div>
                        </DialogContent>}
                </Dialog>
            </div>
            <div className='w-[20%] h-full px-0 py-0'>
                {
                    readOnly ? (<ReadOnlyField value={value[CAR_PLATE_MIDDLE]} />) : (
                        <input
                            ref={(el) => {
                                inputRefs[CAR_PLATE_MIDDLE].current = el;
                            }}
                            onInput={handleInput}
                            value={value[CAR_PLATE_MIDDLE]}
                            type='tel'
                            onChange={(e) => handleInputChange(CAR_PLATE_MIDDLE, e.target.value)}
                            onKeyUp={(e) => handleKeyUp(e, CAR_PLATE_MIDDLE)}
                            maxLength={carPlatePartsMaxLengths[CAR_PLATE_MIDDLE]}
                            className="left-direction borderless-input !text-xl text-center w-full h-full "
                            inputMode="numeric"
                            placeholder=" - - - "
                        />
                    )
                }
            </div>
            <div className="border-l h-full mx-[1%] my-2 border border-[#000000]"></div>
            <div className='flex w-[20%] flex-col items-center gap-1'>
                <PlateIranIcon width={40} height={40} />
                {
                    readOnly ? (<ReadOnlyField value={value[CAR_PLATE_RIGHT]} />) : (
                        <input
                            ref={(el) => {
                                inputRefs[CAR_PLATE_RIGHT].current = el;
                            }}
                            onInput={handleInput}
                            value={value[CAR_PLATE_RIGHT]}
                            type='tel'
                            onChange={(e) => handleInputChange(CAR_PLATE_RIGHT, e.target.value)}
                            onKeyUp={(e) => handleKeyUp(e, CAR_PLATE_RIGHT)}
                            maxLength={carPlatePartsMaxLengths[CAR_PLATE_RIGHT]}
                            className="w-full left-direction borderless-input !text-xl text-center px-0 h-full py-0 "
                            inputMode="numeric"
                            placeholder=" - - "
                        />
                    )
                }
            </div>
        </div>
    );
}
