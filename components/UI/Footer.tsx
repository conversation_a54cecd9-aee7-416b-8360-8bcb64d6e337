import Image from "next/image";
import Link from "next/link";
import Logo from '@/public/assets/images/logo.png';
// import Telegram from '@/public/assets/images/telegram.png';
// import { ChevronUpIcon } from "lucide-react"
import SmoothScrollButton from "./SmoothScrollButton";

const Footer = () => {

   return (
      <footer className="footer bg-white pb-3 p-1">
         <div className="container mx-auto py-8 grid md:grid-cols-2 grid-cols-1 gap-6 ">
            <div className="flex flex-col gap-6 md:p-8 p-3">
               <div className='w-48 md:w-60'> 
                  <Link href="/">
                     <Image
                       src={Logo}
                       alt="logo"
                       width={128}
                       height={64} // 2:1 ratio
                       className="h-auto" // maintain aspect ratio
                       priority
                     />                  
                  </Link>
               </div>
               <div className="flex flex-col gap-2">
                  <h3 className="text-xl max-md:text-lg font-bold">خودراکس – سامانه آنلاین خدمات خودرو</h3>
                  <p className="text-base max-md:text-sm font-normal text-justify leading-8 max-md:leading-7">
                  استعلام خلافی خودرو و موتور، بررسی نمره منفی گواهینامه و پرداخت جریمه‌ ها به‌ صورت سریع و دقیق. تمامی اطلاعات ارائه‌ شده لحظه‌ ای بوده و پرداخت‌ها غیرقابل برگشت هستند.

                     </p>
               </div>
               <a
               href="https://trustseal.enamad.ir/?id=592929&Code=dNFQDyraUjh0biRdvtV1SNbw2GCqa8IL"
               target="_blank"
               className="mt-6"
               rel="noreferrer"
               >
               <Image
                  src="https://trustseal.enamad.ir/logo.aspx?id=592929&Code=dNFQDyraUjh0biRdvtV1SNbw2GCqa8IL"
                  alt="Enamad"
                  width={100}
                  height={100}
                  loading="lazy"
                  
                  style={{ cursor: 'pointer' }}
                  referrerPolicy="origin"
                  
               />
               </a>
            </div>
            <div className="md:pt-16 ">
               <div className="md:grid flex flex-wrap md:grid-cols-2 grid-cols-1 gap-6 max-md:gap-10 p-3 md:p-0">
                        {/* <p className="text-base font-bold md:hidden">دسترسی سریع</p> */}
                  <ul className="flex flex-wrap justify-start gap-x-10 md:flex-col gap-4">
                     <li className="w-full">
                        <p className="text-lg font-black w-full">دسترسی سریع</p>
                        {/* <p className="text-base font-bold hidden md:block">دسترسی سریع</p> */}
                     </li>
                     <li>
                        <Link href="/contact-us" >
                           <span className="text-base font-normal hover:text-primary transition-colors duration-300l">تماس با ما</span>
                        </Link>
                     </li>
                     <li>
                        <Link href="/rules">
                           <span className="text-base font-normal hover:text-primary transition-colors duration-300l"> قوانین و مقررات </span>
                        </Link>
                     </li>
                     {/* <li>
                        <Link href="">
                           <span className="text-base  font-normal">فروشگاه</span>
                        </Link>
                     </li> */}
                     <li>
                        <Link href="/privacy-policy">
                           <span className="text-base font-normal hover:text-primary transition-colors duration-300l"> حفظ حریم خصوصی </span>
                        </Link>
                     </li>

                  </ul>
                  <ul className="w-[60%] flex flex-wrap justify-start gap-x-10 md:flex-col gap-5">
                     <li className="w-full">
                        <p className="text-lg font-black">خدمات محبوب ما </p>
                     </li>
                     {/* <li className="w-full">
                        <Link href="/" >
                           <span className="text-base font-normal hover:text-primary transition-colors duration-300l">خانه</span>
                        </Link>
                     </li> */}
                     <li className="w-full">
                        <Link href="/car-tickets/car-tickets-withdetails">
                           <span className="text-base font-normal hover:text-primary transition-colors duration-300l"> خلافی خودرو با جزئیات </span>
                        </Link>
                     </li>
                     <li className="w-full">
                        <Link href="/car-tickets/car-tickets-withoutdetails">
                           <span className="text-base font-normal hover:text-primary transition-colors duration-300l"> خلافی خودرو بدون جزئیات </span>
                        </Link>
                     </li>
                     <li className="w-full">
                        <Link href="/motor-tickets/motor-tickets-withdetails">
                           <span className="text-base font-normal hover:text-primary transition-colors duration-300l"> خلافی موتور با جزئیات </span>
                        </Link>
                     </li>
                     <li className="w-full">
                        <Link href="/motor-tickets/motor-tickets-withoutdetails">
                           <span className="text-base font-normal hover:text-primary transition-colors duration-300l"> خلافی موتور بدون جزئیات </span>
                        </Link>
                     </li>
                    

                  </ul>
                  {/* <ul className="w-[40%] flex flex-col gap-4">
                     <li>
                        <p className="text-base font-bold text-white">+</p>
                     </li>
                     <li>
                        <Link href="" >
                           <span className="text-base font-normal">خانه</span>
                        </Link>
                     </li>
                     <li>
                        <Link href="/#services">
                           <span className="text-base font-normal">سرویس ها</span>
                        </Link>
                     </li>
                     <li>
                        <Link href="">
                           <span className="text-base  font-normal">فروشگاه</span>
                        </Link>
                     </li>
                     <li>
                        <Link href="">
                           <span className="text-base  font-normal">وبلاگ</span>
                        </Link>
                     </li>

                  </ul> */}
               </div>
            </div>
         </div>

         <div className=" container mx-auto md:px-8 p-3 max-md:px-5 py-6 bg-[#3B38FD] text-white rounded-xl relative ">
            <div className=" w-full inset-1/3 ">
               <div className="bg-[#3B38FD] rounded-full w-[3.8rem] h-16 mx-auto -top-8 p-0.5 flex items-center justify-center absolute md:left-[50%] left-[42%]">
                  {/* <button type="button" className="bg-yellow flex justify-center items-center rounded-full w-12 h-12 p-2">
                     <ChevronUpIcon />
                  </button> */}
                  <SmoothScrollButton />
               </div>
               

            </div>
            <div className="flex md:gap-0 gap-4 flex-col-reverse md:flex-row items-center ">
               <p className="text-sm">© تمامی حقوق محفوظ است.
               </p>
               {/* <ul className="flex mt-5 md:mt-0 items-center gap-4">
                  <li>
                     <Link href="">
                        <Image src={Telegram} alt="telegram" />
                     </Link>
                  </li>
                  <li>
                     <Link href="">
                        <Image src={Telegram} alt="telegram" />
                     </Link>
                  </li>
                  <li>
                     <Link href="">
                        <Image src={Telegram} alt="telegram" />
                     </Link>
                  </li>
                  <li>
                     <Link href="">
                        <Image src={Telegram} alt="telegram" />
                     </Link>
                  </li>
               </ul> */}
            </div>
         </div>
      </footer>
   );
};

export default Footer;