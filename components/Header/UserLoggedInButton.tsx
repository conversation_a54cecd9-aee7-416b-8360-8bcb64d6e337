'use client'

import {ChevronDown, LogOut, WalletCards, History, LayoutDashboard, CircleUserRound, FileClock} from "lucide-react";
import React, {ReactNode} from "react";
import Link from "next/link";
import { useRouter } from 'nextjs-toploader/app';
import {UserPayload} from "@/lib/types/action-types";
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from "../UI/dropdown-menu";
import {PAYMENT_PATH} from "@/lib/routes";
import {logOut} from "@/actions/auth.action";
import ProfileIcon from "@/components/common/svg/ProfileIcon";

type DROPDOWNMENU = {
    title: string,
    icon: ReactNode,
    link?: string,
    onClick?: () => void
}[]

type Props = {
    logOutUser: () => void
    user?: UserPayload
}


export default function UserLoggedInButton({user, logOutUser}: Props) {
    const dropDownMenuItems: DROPDOWNMENU = [
        {
            title: 'افزایش اعتبار',
            icon: <WalletCards/>,
            link: PAYMENT_PATH
        },
        {
            title: 'تاریخچه استعلام',
            icon: <History/>,
            link: "/dashboard/inquiry-history"
        },
        {
            title: 'داشبورد من',
            icon: <LayoutDashboard/>,
            link: '/dashboard'
        },
        {
            title: 'پروفایل من',
            icon: <CircleUserRound/>,
            link: '/dashboard/profile'
        },
        // {
        //     title: 'تاریخچه سفارشات',
        //     icon: <FileClock />,
        //     link: '/dashboard/order-history'
        // },
        {
            title: 'خروج',
            link: '#',
            icon: <LogOut/>,
            onClick: handleLogout
        },
    ]
    const router = useRouter()

    async function handleLogout() {
        await logout();
    }


    async function logout() {
        await logOut()
        logOutUser()
        router.refresh()
    }

    return (
        <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
                <span>
              <div className="flex min-w-[80px] min-h-[40px] cursor-pointer border border-primary/50
                   bg-primary/10 rounded-[99px] p-[5px] gap-1 items-center justify-center">
                   <ChevronDown size={15}/>
                    <div className="text-left flex justify-center items-end gap-1 flex-col">
                        <span className="font-bold text-xs text-primary/80">{user?.phone}</span>
                        <span className="flex justify-end items-center gap-1">
                            <span className="text-xs font-base font-bold">
                                <span
                                    className='text-xs mx-1'> تومان
                                </span>
                                {user?.balance}
                            </span>
                        </span>
                    </div>
                     <ProfileIcon/>
                </div>
                </span>
            </DropdownMenuTrigger>
            <DropdownMenuContent className='md:w-56 w-[200px] z-[900] max-md:text-sm'>
                <div className="bg-gray-100 w-full p-3 rounded-lg my-1 text-sm">
                        <div className="flex items-center gap-2">
                            <span className="">
                                کیف پول: 
                            </span>
                            <p className="relative">
                                <span className="text-green-500  max-md:text-sm"> {user?.balance} </span>
                                <span className="text-sm"> تومان </span>
                            </p>
                        </div>
                    </div>
                {
                    dropDownMenuItems.map((item, index) => (
                        <DropdownMenuItem key={index} asChild>
                            <Link
                                href={item.link || ''}
                                className='flex gap-2 max-md:text-sm items-center justify-start h-[35px] cursor-pointer'
                                onClick={item?.onClick}
                            >
                                <div className="text-gray-600">{item.icon}</div>
                                <span>{item.title}</span>
                            </Link>
                        </DropdownMenuItem>
                    ))
                }
            </DropdownMenuContent>
        </DropdownMenu>
    );
}