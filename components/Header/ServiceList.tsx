"use client"
import Link from "next/link";
import DisplayServiceIcon from "@/components/Services/DisplayServiceIcon";
import {
    CAR_ID_DOCUMENTS_PATH,
    CAR_INSURANCE_PATH,
    CAR_TICKETS_PATH,
    CAR_VIOLATION_IMAGE_PATH,
    DRIVING_LICENSE_POINT_PATH,
    DRIVING_LICENSE_STATUS_PATH,
    MOTOR_TICKETS_PATH,
    PLATE_HISTORY_PATH,
} from "@/lib/routes";
import { ServiceColorVariantType, ServiceStatusType } from "@/lib/types/types";
// import envConfig from "@/lib/config-env";



import { useServiceStatus } from "@/lib/providers/ServicesProvider";
import ServiceIcon from "../common/svg/services/ServiceIcon";
import InstallmentPaymentIcon from "../common/svg/services/InstallmentPaymentIcon";
import { TrafficCone } from "lucide-react";
import VehicleTaxIcon from "../common/svg/services/VehicleTaxIcon";
import TaxIcon from "../common/svg/services/TaxIcon";
import InstallmentIcon from "../common/svg/services/InstallmentIcon";
import RoadIcon from "../common/svg/RoadIcon";
import RoadIconDetailed from "../common/svg/services/RoadIcon";

interface ServiceItemProps {
    href: string;
    name: string;
    icon: React.ReactNode;
    colorVariant?: ServiceColorVariantType;
    status: ServiceStatusType;
    type: "car" | "motor"
}


const ServiceItem: React.FC<ServiceItemProps> = ({ href, name, icon, colorVariant, status }) => {

    const isDisabled = status === 'DEACTIVE'
    return (
        <li className="w-full">
            {isDisabled ? (
                <span className="flex flex-col items-center gap-y-3">
                    <DisplayServiceIcon variant={colorVariant} status={status}>{icon}</DisplayServiceIcon>
                    <Link href={href} className="text-xs text-center px-1">{name}</Link>
                </span>
            ) : (
                <Link href={href} className="flex flex-col items-center gap-y-3">
                    <DisplayServiceIcon variant={colorVariant} status={status}>{icon}</DisplayServiceIcon>
                    <span className="text-xs text-center px-1">{name}</span>
                </Link>
            )}
        </li>
    );
};


export default function ServiceList({ servicesType }: { servicesType: "car" | "motor" }) {

    const { data } = useServiceStatus()

    const services_status = data?.data?.services || {}



    const services: ReadonlyArray<ServiceItemProps> = [
        {
            icon: <ServiceIcon imagePath="car-crash" />,
            href: CAR_TICKETS_PATH, colorVariant: "yellow",
            name: "خلافی خودرو",
            status: services_status?.car_tickets || "ACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="motorcycle" />,
            href: MOTOR_TICKETS_PATH,
            colorVariant: "blue",
            name: "خلافی موتور سیکلت",
            status: services_status?.motor_tickets || "ACTIVE",
            type: "motor"
        },
        {
            icon: <ServiceIcon imagePath="service-camera" />,
            href: CAR_VIOLATION_IMAGE_PATH,
            colorVariant: "purple",
            name: "تصویر تخلفات رانندگی",
            status: services_status?.car_violation_image || "DEACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="service-camera" />,
            href: "/motor-tickets/motor-violation-image",
            colorVariant: "purple",
            name: "تصویر تخلفات رانندگی موتور",
            status: services_status?.motor_violation_image || "DEACTIVE",
            type: "motor"
        },
        {
            icon: <ServiceIcon imagePath="car-overrun" />,
            href: CAR_INSURANCE_PATH,
            colorVariant: "green",
            name: "استعلام بیمه شخص ثالث",
            status: services_status?.car_insurance || "DEACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="car-overrun" />,
            href: "/motor-insurance",
            colorVariant: "green",
            name: "استعلام بیمه شخص ثالث موتور",
            status: services_status?.motor_insurance || "DEACTIVE",
            type: "motor"
        },
        {
            icon: <ServiceIcon imagePath="certificate-service" />,
            href: DRIVING_LICENSE_STATUS_PATH,
            colorVariant: "emerald",
            name: "وضعیت گواهینامه",
            status: services_status?.driving_license_status || "DEACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="certificate-service" />,
            href: DRIVING_LICENSE_STATUS_PATH,
            colorVariant: "emerald",
            name: "وضعیت گواهینامه موتور",
            status: services_status?.motor_driving_license_status || "DEACTIVE",
            type: "motor"
        },
        {
            icon: <ServiceIcon imagePath="document-service" />,
            href: CAR_ID_DOCUMENTS_PATH,
            colorVariant: "blue",
            name: "وضعیت کارت و سند خودرو",
            status: services_status?.carid_decuments || "DEACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="document-service" />,
            href: "/motorid-documents",
            colorVariant: "blue",
            name: "وضعیت کارت و سند موتور",
            status: services_status?.motorid_documents || "DEACTIVE",
            type: "motor"
        },
        {
            icon: <ServiceIcon imagePath="Inquiry-minus-service" />,
            href: DRIVING_LICENSE_POINT_PATH,
            colorVariant: "red",
            name: "استعلام نمره منفی",
            status: services_status?.driving_license_point || "DEACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="Inquiry-minus-service" />,
            href: "/motor-driving-license-point",
            colorVariant: "red",
            name: "استعلام نمره منفی موتور",
            status: services_status?.motor_driving_license_point || "DEACTIVE",
            type: "motor"
        },
        {
            icon: <ServiceIcon imagePath="history-service" />,
            href: PLATE_HISTORY_PATH,
            colorVariant: "indigo",
            name: "استعلام تاریخچه پلاک",
            status: services_status?.plate_history || "DEACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="history-service" />,
            href: "/motor-plate-history-check",
            colorVariant: "indigo",
            name: "استعلام تاریخچه پلاک موتور",
            status: services_status?.motor_plate_history || "DEACTIVE",
            type: "motor"
        },
        {
            icon: <ServiceIcon imagePath="highway-center" />,
            href: "#",
            colorVariant: "indigo",
            name: "استعلام عوارض آزاد راه",
            status: services_status?.freeway || "DEACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="accident-service" />,
            href: "#",
            colorVariant: "lime",
            name: "خرید بیمه بدنه",
            status: services_status?.body_insurance || "DEACTIVE",
            type: "car"
        },
        {
            icon: <InstallmentPaymentIcon />,
            href: "#",
            colorVariant: "blue",
            name: "پرداخت اقساطی خلافی",
            status: services_status?.installment_payment || "DEACTIVE",
            type: "car"
        },

        {
            icon: <TrafficCone stroke="white" size={34} />,
            href: "#",
            colorVariant: "purple",
            name: "استعلام طرح ترافیک",
            status: services_status?.traffic_cone || "DEACTIVE", 
            type: "car"
        },
        {
            icon: <VehicleTaxIcon fill="white" />,
            href: "#",
            colorVariant: "yellow",
            name: "مالیات نقل و انتقال خودرو",
            status: services_status?.vehicle_tax || "DEACTIVE", 
            type: "car"
        },
        {
            icon: <TaxIcon />,
            href: "#",
            colorVariant: "green",
            name: "لیست مالیاتی عوارض سالانه",
            status: services_status?.annual_tax_list || "DEACTIVE", 
            type: "car"
        },
        {
            icon: <TaxIcon />,
            href: "/motor-tax",
            colorVariant: "green",
            name: "مالیات نقل و انتقال موتور",
            status: services_status?.motor_tax || "DEACTIVE", 
            type: "motor"
        },
        {
            icon: <InstallmentIcon />,
            href: "#",
            colorVariant: "indigo",
            name: "عوارض سالینه خودرو",
            status: services_status?.annual_installment || "DEACTIVE", 
            type: "car"
        },
        {
            icon: <RoadIconDetailed />,
            href: "#",
            colorVariant: "indigo",
            name: "پرداخت قبوض عوارض آزادراهی",
            status: services_status?.road_toll || "DEACTIVE", 
            type: "car"
        }
    ]

    return (
        <ul className="w-full grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-y-10 ">
            {services.map((service, index) => (
                service.type === servicesType && (
                    <ServiceItem
                        key={index}
                        {...service}
                    />
                )
            ))}
        </ul>
    );
}
