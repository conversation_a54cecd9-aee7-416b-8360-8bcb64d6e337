import { House } from "lucide-react"
import Link from "next/link"
// import HeaderLocation from "./AddressModal"
// import { getUserAddresses } from "@/lib/utils"
// import { UserAddressesResponse } from "@/lib/types/types"

const ShopNavbar = async () => {
  // const addressList: UserAddressesResponse = await getUserAddresses()
  // console.log(addressList);
  
  return (
    <nav className="container max-md:hidden flex justify-between items-center py-5 mx-auto">
      <div>
        <ul className="flex justify-between gap-8">
          <li>
            <Link className="flex flex-row-reverse gap-2 items-center" href='/'>صفحه اصلی <House /> </Link>
          </li>
          <li>
            <Link href='/#services'>سرویس ها</Link>
          </li>
          <li>
            <Link href='/blog'>بلاگ</Link>
          </li>
          {/* <li>
            <Link href='/category'>فروشگاه</Link>
          </li> */}
          <li>
            <Link href='/contact-us'>تماس با ما</Link>
          </li>
        </ul>
      </div>
      {/* <div className="flex gap-3">
        <span>
          <MapPin />
        </span>
        <span>
          <HeaderLocation addressList={addressList?.data} />
        </span>
      </div> */}
    </nav>
  )
}

export default ShopNavbar