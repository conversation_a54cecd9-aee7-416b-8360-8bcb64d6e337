import Image from "next/image";
import {ServiceColorVariantType, ServiceStatusType} from "@/lib/types/types";
import {cn} from "@/lib/utils";


type Props = {
    children: React.ReactNode;
    variant?: ServiceColorVariantType;
    status?: ServiceStatusType
    containerWidth?: number;
    containerHeight?: number;
    border?: boolean;
    shadow?: boolean;
};

const variantStyles: Record<ServiceColorVariantType, string> = {
    blue: 'bg-gradient-to-b from-[#32CCFE] to-[#1DA1F2] border-blue-100',
    green: 'bg-gradient-to-b from-[#6BE990] to-[#30C25B] border-green-100',
    emerald: 'bg-gradient-to-b from-[#97CBBD] to-[#578C7E] border-emerald-50',
    purple: 'bg-gradient-to-b from-[#AD8BC4] to-[#69578C] border-purple-100',
    yellow: 'bg-gradient-to-b from-[#FFD143] to-[#F8BE0B] border-yellow-100',
    indigo: 'bg-gradient-to-b from-[#7495D1] to-[#314D7F] border-indigo-100',
    lime: 'bg-gradient-to-b from-[#BFE170] to-[#588A1E] border-lime-50',
    red: 'bg-gradient-to-b from-[#FF6D4A] to-[#DB4825] border-red-50',
};


export default function DisplayServiceIcon({
                                               status,
                                               border = true,
                                               containerHeight = 80,
                                               containerWidth = 80,
                                               children,
                                               shadow = true,
                                               variant = 'blue'
                                           }: Props) {
    const isNotActive = status === 'DEACTIVE' 
    return (
        <div className=" relative flex flex-col justify-center items-center gap-2">
            {isNotActive && <div className="absolute top-[-15px] left-[30px]">
                <div className="relative bg-[#9DA5B0] px-2 py-1 rounded-md">
                    <p className="text-white text-[10px] whitespace-nowrap">{status === 'DEACTIVE' ? 'غیر فعال' : 'به زودی'}</p>
                    <div
                        className="absolute bottom-[-4px] left-[5px] border-l-4 border-r-4 border-t-4 border-transparent border-t-[#9DA5B0]"></div>
                </div>
            </div>}
            <div
                style={
                    {
                        height: containerHeight,
                        width: containerWidth,
                    }
                }
                className={cn(`flex justify-center items-center rounded-full ${isNotActive ? "bg-gradient-to-b from-gray-300 to-gray-400 border-gray-100" : variantStyles[variant]}`, {
                    'border-[5px]': border
                })}>
                {children}
            </div>
            {isNotActive && (
  <div className='absolute bottom-0 left-1/2 transform -translate-x-1/2 border border-neutral-100 bg-white w-[28px] h-[28px] rounded-full flex items-center justify-center'>
    <Image 
      src='/assets/images/lock.svg' 
      height={14} 
      width={14} 
      alt='lock'
      style={{ width: 'auto', height: 'auto' }} // Add this line
    />
  </div>
)}
            {shadow && <div className="w-5 h-0.5 bg-black opacity-50 blur-sm rounded-full"></div>}
        </div>
    );
}
