"use client"
import DoubleArrowIcon from "@/components/common/svg/DoubleArrowIcon";
import ShowAllSection from "./ShowAllSection"
import CustomButton from "../UI/CustomButton"
import HomeSection from "@/components/Header/HomeSection";
import ServiceList from "@/components/Header/ServiceList";
import Dots from "@/public/assets/images/dots.webp"
import Image from 'next/image'
import Arrow from "@/public/assets/images/arrow.webp"
import { useState } from "react";

const Services = () => {
    const [activeTab, setActiveTab] = useState<"car" | "motor">("car")
    return (
        <HomeSection>
            <div className="w-full flex flex-col  items-center gap-x-3 gap-y-10">
                <div className="w-full flex justify-center">
                    <div
                        className="w-full flex flex-row lg:justify-center items-end lg:items-start justify-between gap-2">
                        {/* <div className="hidden lg:block">
                    <Image src={Dots} alt="dots" className=' opacity-30'/>
                </div> */}
                        <div className="flex max-md:items-center  gap-x-3">
                            <div className='block  '>
                                <Image src={Dots} alt="dots" className=' opacity-30 md:h-8 md:mt-1' />
                            </div>
                            <div>
                                <div className="whitespace-nowrap max-md:max-w-[300px] font-black flex gap-1 items-center">
                                    <h2 className='font-light text-[#242021] text-xl'>سرویس های خودراکس</h2>
                                    <Image src={Arrow}
                                        alt="arrow" />
                                </div>
                                <p className="mt-1 text-sm md:text-base text-[#242021] font-light">
                                    سرویس و خدمات خودراکس برای خودرو و موتور سیکلت
                                    را اینجا میتوانید ببینید.
                                </p>
                            </div>
                        </div>

                    </div>

                </div>
                <div className="flex items-center border-gray-200 border px-4 py-2 gap-8 font-light rounded-2xl bg-gray-50/75">
                    <button onClick={() => setActiveTab("car")} className={`${activeTab === "car" ? "bg-yellow/70" : ""} px-4 py-2 rounded-2xl text-black`}> خدمات خودرو </button>
                    <button onClick={() => setActiveTab("motor")} className={`${activeTab === "motor" ? "bg-yellow/70" : ""} px-4 py-2 rounded-2xl text-black`}> خدمات موتور سیکلت </button>
                </div>
                <div className="w-full lg:w-2/3 md:h-[390px]">
                    <ServiceList servicesType={activeTab} />
                </div>
            </div>
        </HomeSection>

    )
}

export default Services