'use client'

import {Swiper, SwiperSlide} from 'swiper/react';
import {Navigation, Pagination} from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import '@/styles/swipper.css';
import Image from 'next/image';
import HeaderSubtractIconLeftIcon from "@/components/common/svg/HeaderSubtractIconLeft";
import {ChevronLeft, ChevronRight} from "lucide-react";
import HeaderSubtractIconBottomIcon from "@/components/common/svg/HeaderSubtractIconBottom";
import Link from "next/link";
import React, {ReactNode} from "react";

type SlidesType = { component: string | ReactNode, link: string, title: string, subTitle: string };

const slides: SlidesType[] = [
    {
        component: <BannerComponent/>,
        link: "/#",
        title: 'خدمات آنلاین خودرو با خودراکس',
        subTitle: 'استعلام و پرداخت هوشمند'
    },
];

function BannerComponent() {
    return (
        <div className="relative h-full w-full">
            <Image src='/assets/images/hero-banner.png' alt={`photo-banner`} fill
                   className="!w-full !h-full"/>
            <div
                className="absolute left-[10%] md:left-[20%] top-[15%] w-[40%] sm:w-[30%] md:w-[25%] !h-[70%] md:h-[50%] max-w-[300px]">
                <Image src='/assets/images/banner-hero.webp' alt={`photo-banner`} fill
                       className="w-full h-full"/>
            </div>
        </div>
    )
}


export default function HeroSlider() {
    return (
        <div className="relative w-full h-full">
            <Swiper
                pagination={{
                    clickable: true,
                    el: '.swiper-pagination-custom', // Custom pagination container
                }}
                navigation={{
                    nextEl: '.swiper-button-next-custom',
                    prevEl: '.swiper-button-prev-custom',
                }}
                modules={[Navigation, Pagination]}
                className="w-full h-full overflow-hidden rounded-2xl">
                {slides.map((slide, index) => (
                    <SwiperSlide key={index}>
                        <Link href={slide.link} className="block w-full h-full">
                            <div className="relative h-full flex items-center rounded-2xl">
                                {typeof slide.component === 'string' &&
                                    <Image src={slide.component} alt={`photo-${index}`} fill
                                           className="!w-full !h-full"/>
                                }
                                {React.isValidElement(slide.component) &&
                                    <>
                                        {slide.component}
                                    </>
                                }
                            </div>
                            <h1 className='absolute top-[25%] right-[5%]  md:right-[10%] lg:rigt-[15px] max-w-[180px] md:max-w-[270px] leading-[1.5] text-white text-xl  md:text-3xl'>{slide.title}</h1>
                            <p className='absolute top-[60%] md:top-[55%] right-[5%] md:right-[10%] lg:rigt-[15px] text-base md:text-xl text-white font-light'>{slide.subTitle}</p>
                        </Link>
                    </SwiperSlide>
                ))}
            </Swiper>
            {slides.length > 1 && (
                <>

            <span className='absolute top-1/2 transform -translate-y-1/2 left-[-2px] md:left-[-3px] z-10'>
                <HeaderSubtractIconLeftIcon width={20} height={20} className='!w-[25px] md:!w-[35px]'/>
            </span>

                    <span className='absolute top-1/2 transform -translate-y-1/2 right-[-2px] md:right-[-3px] z-10'>
                <HeaderSubtractIconLeftIcon width={20} height={20}
                                            className='!w-[25px] transform rotate-180 md:!w-[35px]'/>
            </span>
                </>)
            }

            {
                slides.length > 1 && (
                    <>
                        <span className=' absolute left-1/2 transform -translate-x-1/2 bottom-0 z-10'>
                            <span
                                className="swiper-pagination-custom  text-center absolute flex justify-center items-center  left-1/2 transform top-1/2  z-20">
                            </span>
                            <div className='relative h-full flex items-center rounded-2xl'>
                                <div>
                                    <HeaderSubtractIconBottomIcon
                                        width={20}
                                        height={20}
                                        className='!w-[90px] md:!w-[100px]'
                                    />
                                </div>
                            </div>
                        </span>
                        <div
                            className="swiper-button-next-custom absolute border-2 bg-[#F3F4F6] border-white left-[-5px] top-1/2 transform -translate-y-1/2 z-20 p-1 md:p-2 rounded-full shadow-lg cursor-pointer transition">
                            <ChevronLeft size={14}/>
                        </div>
                        <div
                            className="swiper-button-prev-custom absolute border bg-[#F3F4F6] border-white right-[-5px]  top-1/2 transform -translate-y-1/2 z-20 p-1 md:p-2 rounded-full shadow-lg cursor-pointer transition">
                            <ChevronRight size={14}/>
                        </div>
                    </>

                )
            }

        </div>
    );
}
