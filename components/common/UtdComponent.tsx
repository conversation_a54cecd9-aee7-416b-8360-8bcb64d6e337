"use client";
import { useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import page from "@/app/(dashboard)/dashboard/page";
import CookieService from "@/lib/services/cookie-service";
import { trackUTMFromServer } from "@/actions/other.action";
import { v1 as uuidv1 } from 'uuid';



const UtdTracker = () => {
    const pathname = usePathname();
    const searchParams = useSearchParams();
    
  useEffect(() => {
    async function udp() {
      const uTraceKey = "u_trace";
      let uTraceId = localStorage.getItem(uTraceKey);
      if (!uTraceId) {
        uTraceId = uuidv1();
        localStorage.setItem(uTraceKey, uTraceId);
      }

      const fullURL = window.location.href;

      const localStorageData: Record<string, string> = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          const value = localStorage.getItem(key);
          if (value) localStorageData[key] = value;
        }
      }

      const data = await trackUTMFromServer(fullURL, uTraceId, localStorageData);
      console.log("UTM response:", data);

      if (data?.data?.utm) {
        Object.entries(data.data.utm).forEach(([key, value]) => {
          localStorage.setItem(key, String(value));
        });
      }
    }

    udp();
  }, [pathname, searchParams]);

  return null;
};


export default UtdTracker;
