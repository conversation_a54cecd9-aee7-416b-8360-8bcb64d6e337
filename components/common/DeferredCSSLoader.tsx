'use client';

import { useEffect } from 'react';

/**
 * DeferredCSSLoader - Loads non-critical CSS after page load
 * This helps reduce the initial CSS bundle size and improves Lighthouse scores
 */
export default function DeferredCSSLoader() {
  useEffect(() => {
    // Load deferred CSS after the page has loaded
    const loadDeferredCSS = () => {
      // Check if deferred CSS is already loaded
      if (document.querySelector('link[data-deferred-css]')) {
        return;
      }

      // Load deferred CSS
      const deferredLink = document.createElement('link');
      deferredLink.rel = 'stylesheet';
      deferredLink.href = '/styles/deferred.css';
      deferredLink.setAttribute('data-deferred-css', 'true');
      deferredLink.media = 'print'; // Load as print media first

      // Add to head
      document.head.appendChild(deferredLink);

      // Change media to all after load to apply styles
      deferredLink.onload = () => {
        deferredLink.media = 'all';
      };

      // Load minimal CSS files for components
      const cssFiles = [
        { href: '/styles/minimal-swiper.css', id: 'swiper' },
        { href: '/styles/minimal-leaflet.css', id: 'leaflet' },
        { href: '/styles/minimal-rc-slider.css', id: 'rc-slider' },
      ];

      cssFiles.forEach(({ href, id }) => {
        if (!document.querySelector(`link[data-css-id="${id}"]`)) {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = href;
          link.setAttribute('data-css-id', id);
          link.media = 'print';

          document.head.appendChild(link);

          link.onload = () => {
            link.media = 'all';
          };
        }
      });
    };

    // Load after a short delay to ensure critical rendering is complete
    const timer = setTimeout(loadDeferredCSS, 100);

    return () => clearTimeout(timer);
  }, []);

  return null; // This component doesn't render anything
}

/**
 * Hook to dynamically load specific CSS modules when needed
 */
export const useDynamicCSS = () => {
  const loadCSS = (cssPath: string, id: string) => {
    // Check if CSS is already loaded
    if (document.querySelector(`link[data-css-id="${id}"]`)) {
      return Promise.resolve();
    }

    return new Promise<void>((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = cssPath;
      link.setAttribute('data-css-id', id);
      
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`Failed to load CSS: ${cssPath}`));
      
      document.head.appendChild(link);
    });
  };

  const loadSwiperCSS = () => loadCSS('/styles/minimal-swiper.css', 'swiper');
  const loadLeafletCSS = () => loadCSS('/styles/minimal-leaflet.css', 'leaflet');
  const loadRCSliderCSS = () => loadCSS('/styles/minimal-rc-slider.css', 'rc-slider');

  return {
    loadCSS,
    loadSwiperCSS,
    loadLeafletCSS,
    loadRCSliderCSS,
  };
};
