import React from "react";

const LicenseIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 37.453}
    height={props.height || 32.958}
    viewBox="0 0 37.453 32.958"
    {...props}
  >
    <defs>
      <linearGradient
        id="license-gradient"
        x1="0.5"
        x2="0.5"
        y2="1"
        gradientUnits="objectBoundingBox"
      >
        <stop offset="0" stopColor="#fff" />
        <stop offset="1" stopColor="#fff" stopOpacity="0.639" />
      </linearGradient>
    </defs>
    <g transform="translate(-2.341 -7.022)">
      <path
        d="M21.9,34.09H4.341A2.341,2.341,0,0,1,2,31.749V8.341A2.341,2.341,0,0,1,4.341,6H37.112a2.341,2.341,0,0,1,2.341,2.341V20.478A9.761,9.761,0,0,0,23.664,31.936L22.576,33.27ZM7.852,13.022V14.9h19.9V13.022Zm0,6.554H20.726V17.7H7.852Zm0,8.193h11.7V25.9H7.852Z"
        transform="translate(0.341 1.022)"
        fill="url(#license-gradient)"
      />
      <path
        d="M36,24.678A7.455,7.455,0,1,0,23.4,30.05l-2.271,2.774,1.053,4.225,4.284-5.22a7.327,7.327,0,0,0,4.155,0l4.284,5.22,1.053-4.225L33.684,30.05A7.42,7.42,0,0,0,36,24.678Zm-12.57,0a5.109,5.109,0,1,0,.012,0Z"
        transform="translate(3.594 2.931)"
        fill="url(#license-gradient)"
      />
    </g>
  </svg>
);

export default LicenseIcon;
