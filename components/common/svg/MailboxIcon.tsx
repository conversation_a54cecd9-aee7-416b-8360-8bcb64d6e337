import React from "react";

type MailboxIconProps = React.SVGProps<SVGSVGElement> & {
  color?: string;
  size?: number;
};

const MailboxIcon: React.FC<MailboxIconProps> = ({
  color = "#9da5b0",
  size = 24,
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 19.5 21.5"
      fill="none"
      {...props}
    >
      <path
        d="M14,20.75a.75.75,0,0,0,1.5,0Zm-2.25-3V17a.75.75,0,0,0-.75.75Zm-.75,3a.75.75,0,0,0,1.5,0Zm2-15a.75.75,0,0,0,1.5,0ZM16.75,1.5a.75.75,0,0,0,0-1.5ZM15.5,20.75v-3H14v3ZM14.75,17h-3v1.5h3ZM11,17.75v3h1.5v-3Zm-1-9v5h1.5v-5ZM6.75,17h-2v1.5h2ZM1.5,13.75v-5H0v5ZM4.75,17A3.25,3.25,0,0,1,1.5,13.75H0A4.75,4.75,0,0,0,4.75,18.5ZM10,13.75A3.25,3.25,0,0,1,6.75,17v1.5a4.75,4.75,0,0,0,4.75-4.75ZM5.75,4.5A4.25,4.25,0,0,1,10,8.75h1.5A5.75,5.75,0,0,0,5.75,3Zm0-1.5A5.75,5.75,0,0,0,0,8.75H1.5A4.25,4.25,0,0,1,5.75,4.5Zm0,1.5h8V3h-8ZM18,8.75v5h1.5v-5ZM14.75,17h-10v1.5h10ZM18,13.75A3.25,3.25,0,0,1,14.75,17v1.5a4.75,4.75,0,0,0,4.75-4.75ZM13.75,4.5A4.25,4.25,0,0,1,18,8.75h1.5A5.75,5.75,0,0,0,13.75,3Zm.75,1.25v-3H13v3ZM15.75,1.5h1V0h-1ZM14.5,2.75A1.25,1.25,0,0,1,15.75,1.5V0A2.75,2.75,0,0,0,13,2.75Z"
        fill={color}
      />
    </svg>
  );
};

export default MailboxIcon;
