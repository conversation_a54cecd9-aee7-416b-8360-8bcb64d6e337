import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  width?: number | string;
  height?: number | string;
  fill?: string;
}

const CardCheckIcon: React.FC<IconProps> = ({
  width = 15.125,
  height = 13.625,
  fill = "#9da5b0",
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 15.125 13.625"
    className={className}
    {...props}
  >
    <path
      d="M8.266,13.625a.524.524,0,1,0,0-1.048Zm5.8-5.415a.528.528,0,0,0,1.055,0ZM14.6,3.319h0ZM8.266,12.577H3.342v1.048H8.266ZM1.055,10.306V3.319H0v6.987ZM3.342,1.048h8.442V0H3.342ZM14.07,3.319V8.21h1.055V3.319Zm-13.015,0A2.279,2.279,0,0,1,3.342,1.048V0A3.33,3.33,0,0,0,0,3.319Zm2.286,9.258a2.279,2.279,0,0,1-2.286-2.271H0a3.33,3.33,0,0,0,3.342,3.319ZM11.783,1.048A2.279,2.279,0,0,1,14.07,3.319h1.055A3.33,3.33,0,0,0,11.783,0Z"
      fill={fill}
    />
    <path
      d="M1.407.7A.7.7,0,1,1,.7,0,.7.7,0,0,1,1.407.7Z"
      transform="translate(2.638 9.607)"
      fill={fill}
    />
    <path
      d="M14.6.524h.528A.526.526,0,0,0,14.6,0ZM.528.524V0A.526.526,0,0,0,0,.524ZM14.6,3.319v.524a.526.526,0,0,0,.528-.524Zm-14.07,0H0a.522.522,0,0,0,.155.371.529.529,0,0,0,.373.153ZM14.6,0H.528V1.048H14.6ZM14.07.524V3.319h1.055V.524ZM14.6,2.795H.528V3.843H14.6ZM1.055,3.319V.524H0V3.319Z"
      transform="translate(0 2.795)"
      fill={fill}
    />
    <path
      d="M.857,1.512a.53.53,0,0,0-.742.082A.522.522,0,0,0,.2,2.331ZM5.146.869A.522.522,0,0,0,5.1.13a.53.53,0,0,0-.745.049ZM1.761,2.9l-.33.409ZM.2,2.331l1.233.98.659-.818L.857,1.512Zm2.929.83L5.146.869,4.351.179,2.333,2.471Zm-1.7.15a1.237,1.237,0,0,0,1.7-.15l-.794-.69a.177.177,0,0,1-.242.021Z"
      transform="translate(9.849 9.782)"
      fill={fill}
    />
  </svg>
);

export default CardCheckIcon;
