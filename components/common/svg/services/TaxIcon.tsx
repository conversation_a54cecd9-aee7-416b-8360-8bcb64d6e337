import React from "react";

type IconProps = React.SVGProps<SVGSVGElement> & {
  startColor?: string;
  endColor?: string;
};

const TaxIcon: React.FC<IconProps> = ({
  startColor = "#fff",
  endColor = "#fff",
  ...props
}) => {
  // const gradientId = `vehicle-tax-gradient-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 32.852 33.967"
      width={props.width || 32.852}
      height={props.height || 33.967}
      fill="currentColor"
      {...props}
    >
      <defs>
        <linearGradient id={`tax-gradient`} x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
          <stop offset="0" stopColor={startColor} />
          <stop offset="1" stopColor={endColor} stopOpacity={0.639} />
        </linearGradient>
      </defs>

      <g>
        <path
          d="M5.949,8.4H22.706V9.6H5.949Zm0,4.2H22.706v1.2H5.949Zm0,4.2H22.706V18H5.949Zm20.979,6.168V6.812A5.687,5.687,0,0,0,21.256,1.14H1.727V22.974h25.2Z"
          transform="translate(2.099 -1.14)"
          fill={`url(#${`tax-gradient`})`}
          fillRule="evenodd"
        />
        <path
          d="M30.241,4.143H1.215V15.678H34.066V4.143H30.241ZM8.156,6.809h5.826V8.341H12.027v4.671H10.11V8.341H8.156V6.809Zm9.922,5.179H15.894l-.3,1.024H13.636l2.331-6.2h2.09l2.332,6.2H18.383Zm-.4-1.342-.686-2.23-.682,2.23Zm2.9-3.837h2.11l1.1,1.908,1.066-1.908h2.091l-1.929,3,2.111,3.2H24.971l-1.223-1.993L22.52,13.012H20.38L22.52,9.78,20.575,6.809Z"
          transform="translate(-1.215 18.289)"
          fill={`url(#${`tax-gradient`})`}
          fillRule="evenodd"
        />
      </g>
    </svg>
  );
};

export default TaxIcon;
