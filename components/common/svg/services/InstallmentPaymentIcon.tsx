import React from "react";

type IconProps = React.SVGProps<SVGSVGElement>;

const InstallmentPaymentIcon: React.FC<IconProps> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 42.6 36.732"
      width={props.width || 42.6}
      height={props.height || 36.732}
      {...props}
    >
      <defs>
        <linearGradient
          id="installment-gradient"
          x1="0.5"
          x2="0.5"
          y2="1"
          gradientUnits="objectBoundingBox"
        >
          <stop offset="0" stopColor="#fff" />
          <stop offset="1" stopColor="#fff" stopOpacity="0.639" />
        </linearGradient>
      </defs>
      <g transform="translate(-94.5 -98.682)">
        <g transform="translate(94.5 98.682)">
          <path
            d="M40.085,15H42.46a.994.994,0,0,0,.985-.985V9.985A.994.994,0,0,0,42.46,9H40.085a.994.994,0,0,0-.985.985v4.032A.994.994,0,0,0,40.085,15Z"
            transform="translate(-22.705 -9)"
            fill="url(#installment-gradient)"
          />
          <path
            d="M73.785,15H76.16a.994.994,0,0,0,.985-.985V9.985A.994.994,0,0,0,76.16,9H73.785a.994.994,0,0,0-.985.985v4.032A.994.994,0,0,0,73.785,15Z"
            transform="translate(-41.309 -9)"
            fill="url(#installment-gradient)"
          />
          <path
            d="M55.132,17.1H51.907v1.389a2.715,2.715,0,0,1-2.732,2.732H46.845a2.743,2.743,0,0,1-2.732-2.732V17.1H36.856v1.389a2.715,2.715,0,0,1-2.732,2.732H31.749a2.743,2.743,0,0,1-2.732-2.732V17.1H25.792A1.8,1.8,0,0,0,24,18.892V28.971A11.934,11.934,0,0,1,35.6,40.886a11.121,11.121,0,0,1-.314,2.732H55.177a1.8,1.8,0,0,0,1.792-1.792V18.892A1.866,1.866,0,0,0,55.132,17.1ZM45.949,37.885V39a.4.4,0,0,1-.4.4H44.292a.4.4,0,0,1-.4-.4V37.929a2.76,2.76,0,0,1-2.419-2.419.83.83,0,0,1,.09-.314.407.407,0,0,1,.314-.134h1.254a.43.43,0,0,1,.4.314.675.675,0,0,0,.672.538h1.075a1.083,1.083,0,0,0,1.12-.941.9.9,0,0,0-.269-.806,1.049,1.049,0,0,0-.806-.358h-.806a3.213,3.213,0,0,1-3.225-2.822,3.07,3.07,0,0,1,2.553-3.36v-1.12a.4.4,0,0,1,.4-.4H45.5a.4.4,0,0,1,.4.4v1.075A2.76,2.76,0,0,1,48.323,30a.83.83,0,0,1-.09.314.407.407,0,0,1-.314.134H46.666a.43.43,0,0,1-.4-.314.642.642,0,0,0-.672-.538H44.516a1.083,1.083,0,0,0-1.12.941.9.9,0,0,0,.269.806,1.122,1.122,0,0,0,.806.358h.941a2.963,2.963,0,0,1,2.285,1.03,3.193,3.193,0,0,1,.806,2.419A3.234,3.234,0,0,1,45.949,37.885Z"
            transform="translate(-14.369 -13.472)"
            fill="url(#installment-gradient)"
          />
          <path
            d="M11.772,49.5a9.295,9.295,0,1,0,9.272,9.317A9.291,9.291,0,0,0,11.772,49.5ZM15.58,60.116H11.772a1.348,1.348,0,0,1-1.344-1.344V54.293a1.344,1.344,0,1,1,2.688,0v3.18H15.58a1.348,1.348,0,0,1,1.344,1.344A1.31,1.31,0,0,1,15.58,60.116Z"
            transform="translate(-2.5 -31.358)"
            fill="url(#installment-gradient)"
          />
        </g>
      </g>
    </svg>
  );
};

export default InstallmentPaymentIcon;
