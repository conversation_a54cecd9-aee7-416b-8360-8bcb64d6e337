import React from "react";

type IconProps = React.SVGProps<SVGSVGElement> & {
  startColor?: string;
  endColor?: string;
};

const InstallmentIcon: React.FC<IconProps> = ({
  startColor = "#fff",
  endColor = "#fff",
  ...props
}) => {
  // const gradientId = `installment-gradient-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 41.269 39"
      width={props.width || 41.269}
      height={props.height || 39}
      fill="currentColor"
      {...props}
    >
      <defs>
        <linearGradient id={`installment-gradient`} x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
          <stop offset="0" stopColor={startColor} />
          <stop offset="1" stopColor={endColor} stopOpacity={0.639} />
        </linearGradient>
      </defs>

      <g transform="translate(-6.751 -9.191)">
        <path
          d="M36.735,12.715H34.271V10.684a1.493,1.493,0,0,0-2.986,0v2.031H25.6V10.684a1.493,1.493,0,0,0-2.986,0v2.031H16.92V10.684a1.493,1.493,0,0,0-2.986,0v2.031H11.469a4.719,4.719,0,0,0-4.719,4.719V38.907a4.714,4.714,0,0,0,4.719,4.7H28.418A11.706,11.706,0,0,1,41.453,27.916V17.433a4.719,4.719,0,0,0-4.719-4.719ZM16.1,39.265H14.291a1.493,1.493,0,0,1,0-2.986H16.1a1.493,1.493,0,0,1,0,2.986Zm0-5.689H14.291a1.493,1.493,0,0,1,0-2.986H16.1a1.493,1.493,0,0,1,0,2.986Zm0-5.689H14.291a1.493,1.493,0,0,1,0-2.986H16.1a1.493,1.493,0,0,1,0,2.986ZM25,39.265H23.191a1.493,1.493,0,0,1,0-2.986H25a1.493,1.493,0,0,1,0,2.986Zm0-5.689H23.191a1.493,1.493,0,0,1,0-2.986H25a1.493,1.493,0,0,1,0,2.986Zm0-5.689H23.191a1.493,1.493,0,1,1,0-2.986H25a1.493,1.493,0,0,1,0,2.986Zm8.9,0H32.09a1.493,1.493,0,0,1,0-2.986H33.9a1.493,1.493,0,0,1,0,2.986Zm0-6.839H14.291a1.493,1.493,0,0,1,0-2.986H33.9a1.493,1.493,0,0,1,0,2.986ZM39.349,30.7c-11.554.373-11.625,17.07,0,17.488,11.6-.427,11.518-17.105,0-17.488Zm-3.364,3.886a1.494,1.494,0,1,1-1.443,1.493A1.493,1.493,0,0,1,35.986,34.589Zm2.221,8.1h0a1.493,1.493,0,0,1-2.111-2.111l4.385-4.386A1.493,1.493,0,0,1,42.59,38.3Zm4.493,1.6h0A1.494,1.494,0,1,1,44.142,42.8,1.493,1.493,0,0,1,42.7,44.29Z"
          fill={`url(#${`installment-gradient`})`}
        />
      </g>
    </svg>
  );
};

export default InstallmentIcon;
