import React from "react";

type IconProps = React.SVGProps<SVGSVGElement> & {
  startColor?: string;
  endColor?: string;
};

const VehicleTaxIcon: React.FC<IconProps> = ({
  startColor = "#fff",
  endColor = "#fff",
  ...props
}) => {
  const gradientId = `vehicle-tax-gradient-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 42.911 40.59"
      width={props.width || 42.911}
      height={props.height || 40.59}
      fill="currentColor"
      {...props}
    >
      <defs>
        <linearGradient id={`vehicle-tax-gradient`}  x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
          <stop offset="0" stopColor={startColor} />
          <stop offset="1" stopColor={endColor} stopOpacity={0.639} />
          
        </linearGradient>
      </defs>
      <g transform="translate(-4.688 -7.387)">
        <path
          d="M25.09,34.559a.4.4,0,0,0-.209-.075H23.094a.313.313,0,0,0-.312.312v.7a.313.313,0,0,0,.312.312h1.7a.285.285,0,0,1,.118.024l1.516.628a.311.311,0,0,1,.175.389,4.187,4.187,0,0,1-.856,1.445l-.419.463a3.2,3.2,0,0,0-.831,2.152v6.758a.311.311,0,0,0,.312.312h2.443a.313.313,0,0,0,.312-.312V44.933a.3.3,0,0,1,.1-.229.31.31,0,0,1,.239-.081,75.269,75.269,0,0,0,14.57,0,.31.31,0,0,1,.34.308v2.733a.311.311,0,0,0,.312.312h2.443a.313.313,0,0,0,.312-.312V40.906a3.2,3.2,0,0,0-.831-2.152l-.419-.463a4.157,4.157,0,0,1-.856-1.445.311.311,0,0,1,.175-.389l1.516-.628a.3.3,0,0,1,.12-.024h1.7a.311.311,0,0,0,.312-.312v-.7a.313.313,0,0,0-.312-.312H45.5a.315.315,0,0,0-.167.049L43.8,35.5a.306.306,0,0,1-.277.026.313.313,0,0,1-.19-.205l-1.367-4.791a2.587,2.587,0,0,0-2.487-1.875H30.905a2.587,2.587,0,0,0-2.487,1.875l-1.369,4.791a.312.312,0,0,1-.465.179l-1.492-.944Zm-3.059.237A1.066,1.066,0,0,1,23.1,33.732h1.8a1.077,1.077,0,0,1,.6.192l.993.628,1.208-4.225A3.338,3.338,0,0,1,30.9,27.906h7.891V11.656a4.269,4.269,0,0,0-4.27-4.27H8.907a4.22,4.22,0,0,0-4.219,4.219v6.243a.752.752,0,0,0,.752.752h6.183V44.2a.752.752,0,0,0,.752.752H23.743V40.9a3.954,3.954,0,0,1,1.027-2.66l.419-.463a3.4,3.4,0,0,0,.544-.8l-1.027-.425H23.092a1.06,1.06,0,0,1-1.065-1.08v-.679l.752.751M32.92,42.357h4.531a.623.623,0,0,0,0-1.245H32.92a.623.623,0,0,0,0,1.245Zm-5.2-.4h2.637v-.6l-2.637-1.11Zm12.3-.6v.6h2.637v-1.7l-2.637,1.11Zm.235-9.209,1.257,4.4a53.946,53.946,0,0,1-12.644,0l1.258-4.4a2.165,2.165,0,0,1,2.08-1.571h5.965a2.166,2.166,0,0,1,2.082,1.571ZM17.577,31.084h6.935a.752.752,0,0,0,0-1.5H17.577a.752.752,0,0,0,0,1.5Zm0-3.467h6.935a.752.752,0,0,0,0-1.5H17.577a.752.752,0,0,0,0,1.5Zm0-3.527H32.836a.752.752,0,0,0,0-1.5H17.577a.752.752,0,1,0,0,1.5ZM27.586,16.3a2.535,2.535,0,1,0,2.534,2.536A2.537,2.537,0,0,0,27.586,16.3Zm.651-3.939-7.129,7.129a.753.753,0,0,0,1.065,1.065L29.3,13.43a.753.753,0,0,0-1.065-1.065Zm-.651,5.444a1.03,1.03,0,1,1-1.031,1.031A1.031,1.031,0,0,1,27.586,17.81ZM8.9,8.89A2.715,2.715,0,0,0,6.191,11.6v5.49h5.43V11.61a3.168,3.168,0,0,0-.058-.572,2.883,2.883,0,0,0-.124-.418l-.008-.019c-.109-.205-.19-.365-.26-.493a2.75,2.75,0,0,0-.391-.468c-.071-.056-.134-.119-.214-.184-.041-.032-.083-.064-.124-.092l-.009-.006c-.132-.083-.325-.15-.547-.288A2.828,2.828,0,0,0,8.9,8.888Zm13.923,2.658a2.535,2.535,0,1,0,2.534,2.534A2.535,2.535,0,0,0,22.827,11.548Zm0,1.5A1.03,1.03,0,1,1,21.8,14.082,1.029,1.029,0,0,1,22.827,13.053Z"
          id={`vehicle-tax-gradient`}
          fillRule="evenodd"
        />
      </g>
    </svg>
  );
};

export default VehicleTaxIcon;
