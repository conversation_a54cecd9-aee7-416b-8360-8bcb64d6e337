import React from "react";

type IconProps = React.SVGProps<SVGSVGElement> & {
  startColor?: string;
  endColor?: string;
};

const RoadIconDetailed: React.FC<IconProps> = ({
  startColor = "#fff",
  endColor = "#fff",
  ...props
}) => {
  const gradientId = `road-gradient-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 33.518 33.978"
      width={props.width || 33.518}
      height={props.height || 33.978}
      fill="currentColor"
      {...props}
    >
      <defs>
        <linearGradient
          id={gradientId}
          x1="0.5"
          x2="0.5"
          y2="1"
          gradientUnits="objectBoundingBox"
        >
          <stop offset="0" stopColor={startColor} />
          <stop offset="1" stopColor={endColor} stopOpacity={0.639} />
        </linearGradient>
      </defs>

      <path
        d="M35.664,29.891a10.914,10.914,0,0,0-3.4-18.122L31.247,6.446A1.529,1.529,0,0,0,29.774,5.2H22.186V7.239h-1.7V5.2H13.5A1.5,1.5,0,0,0,12.03,6.408L5.65,37.366a1.509,1.509,0,0,0,1.472,1.812H20.487V37.1h1.7v2.076h13.4A1.494,1.494,0,0,0,37.061,37.4ZM20.487,10.863h1.7v1.925a14.887,14.887,0,0,0-1.7,1.359Zm1.7,22.614h-1.7V29.664a9.587,9.587,0,0,0,1.7,1.359Zm6-2.341a9.231,9.231,0,1,1,9.25-9.212A9.2,9.2,0,0,1,28.189,31.137Z"
        transform="translate(-5.62 -5.2)"
        fill={`url(#${gradientId})`}
      />

      <path
        d="M60.714,31.77a.691.691,0,0,1-.6-.68v-.264c0-.151-.113-.227-.3-.227H57.883c-.227,0-.3.076-.3.227v.264a.691.691,0,0,1-.6.68c-1.359.189-2.076.906-2.076,2.114v2.756a1.351,1.351,0,0,0,.982,1.472L58,38.641l1.095.264a1.175,1.175,0,0,1,1.019,1.246v1.019c0,.415-.264.6-.793.6h-.944c-.491,0-.793-.189-.793-.6v-.944c0-.038,0-.038-.038-.038v-.038c0-.038-.038-.038-.038-.076A1056.874,1056.874,0,0,1,57.429,40l-.038-.038-.038-.038-.038-.038h-.038a.037.037,0,0,1-.038-.038H57.2a.546.546,0,0,0-.264-.076H55.844a.888.888,0,0,0-.944.868v.944a1.045,1.045,0,0,0,.038.34h0c0,.076.038.113.038.189h0c0,.076.038.113.038.151h0a.286.286,0,0,0,.076.151v.038c.038.038.038.076.076.151v.038h0c.038.038.038.076.076.113a.037.037,0,0,0,.038.038c.038.038.038.076.076.113l.038.038c.038.038.076.076.076.113l.038.038c.038.038.076.076.113.076l.038.038c.038.038.076.038.113.076.038,0,.038.038.076.038.038.038.076.038.113.076.038,0,.038.038.076.038s.076.038.113.038a.132.132,0,0,1,.076.038c.038,0,.076.038.113.038s.076.038.113.038.076.038.113.038.076.038.151.038c.038,0,.038,0,.076.038.076,0,.151.038.227.038a.691.691,0,0,1,.6.68v.264c0,.151.113.227.3.227h1.925c.189,0,.3-.076.3-.227v-.264a.691.691,0,0,1,.6-.68,2.391,2.391,0,0,0,1.812-.982,2.2,2.2,0,0,0,.3-1.133h0V39.812h0a2.215,2.215,0,0,0-.642-1.963,3.07,3.07,0,0,0-1.17-.566l-1.284-.3-1.623-.415a.682.682,0,0,1-.529-.642V34.375c0-.378.3-.6.793-.6h.944c.529,0,.793.227.793.6v.566c0,.151.038.717.982.831H62a.781.781,0,0,0,.793-.868V33.96h0v-.076C62.79,32.639,62.073,31.959,60.714,31.77Z"
        transform="translate(-36.295 -21.011)"
        fill={`url(#${gradientId})`}
      />
    </svg>
  );
};

export default RoadIconDetailed;
