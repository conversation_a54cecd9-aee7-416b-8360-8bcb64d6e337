import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  width?: number | string;
  height?: number | string;
  fill?: string;
}

const HelpOperatorIcon: React.FC<IconProps> = ({
  width = 31.334,
  height = 37.693,
  fill = "#00a76f",
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 31.334 37.693"
    className={className}
    {...props}
  >
    <path
      d="M72.9,88.371a11.658,11.658,0,0,0-6.166-9.523c-1.392-.676-3.915-2-5.569-2.876,2.119-2.41,2.745-6.34,2.843-6.9a27.668,27.668,0,0,0,.22-5.662c-.4-6.09-6.319-6.185-6.938-6.179h-.1c-.562-.008-6.541.054-6.948,6.179a27.669,27.669,0,0,0,.22,5.662c.029.163.1.617.246,1.231a7.5,7.5,0,0,0,4.246,2.631.964.964,0,0,1,.687-.291h2.573a1.026,1.026,0,0,1,0,2.05H55.639a.986.986,0,0,1-.905-.618,9.735,9.735,0,0,1-3.359-1.527A11.016,11.016,0,0,0,53.3,75.973c-1.654.875-4.176,2.2-5.569,2.875a11.66,11.66,0,0,0-6.166,9.523s-.1,1.12,1.941,1.544a64.206,64.206,0,0,0,27.452,0C73,89.492,72.9,88.371,72.9,88.371Z"
      transform="translate(-41.568 -53.708)"
      fill={fill}
    />
    <path
      d="M110.14,17.549h.613V9.96c0-.019.006-.037.006-.056V8.783A6.666,6.666,0,0,1,112.5,3.97c2.388-2.475,6.468-2.442,6.537-2.431.048,0,4.094-.065,6.493,2.408a6.679,6.679,0,0,1,1.752,4.836V9.968c0,.012,0,.022,0,.034v7.548h.613a2.809,2.809,0,0,0,2.733-2.879V11.9a2.867,2.867,0,0,0-1.889-2.737V8.783a8.2,8.2,0,0,0-2.191-5.936C123.718-.073,119.192,0,119.034,0c-.19,0-4.72-.06-7.553,2.866A8.187,8.187,0,0,0,109.3,8.783v.382a2.868,2.868,0,0,0-1.891,2.738V14.67A2.809,2.809,0,0,0,110.14,17.549Z"
      transform="translate(-103.352 0)"
      fill={fill}
    />
  </svg>
);

export default HelpOperatorIcon;
