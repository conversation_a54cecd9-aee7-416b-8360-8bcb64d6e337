import Image from "next/image";
import React from "react";
import SupportFullDay from "@/public/assets/images/24-hours-support.svg";
interface IconProps extends React.SVGProps<SVGSVGElement> {
  width?: number
  height?: number
  fill?: string;
}

const SupportIcon: React.FC<IconProps> = ({
  width = 35.562,
  height = 35.169,
  fill = "#1f84fb",
  className,
  ...props
}) => (
  <Image src={SupportFullDay} width={width} height={height} alt="support" />
);

export default SupportIcon;
