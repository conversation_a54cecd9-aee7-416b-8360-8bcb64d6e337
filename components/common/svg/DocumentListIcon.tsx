import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  width?: number | string;
  height?: number | string;
  fill?: string;
}

const DocumentListIcon: React.FC<IconProps> = ({
  width = 28.666,
  height = 33.444,
  fill = "#f7bc06",
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 28.666 33.444"
    className={className}
    {...props}
  >
    <path
      d="M13.555,28.277a2.389,2.389,0,1,1-2.389-2.389A2.391,2.391,0,0,1,13.555,28.277Zm-2.389-21.5a2.389,2.389,0,1,0,2.389,2.389A2.391,2.391,0,0,0,11.167,6.778Zm2.389,11.944a2.389,2.389,0,1,1-2.389-2.389A2.391,2.391,0,0,1,13.555,18.722Zm-1.194,0a1.194,1.194,0,1,0-1.194,1.194A1.195,1.195,0,0,0,12.361,18.722ZM32.666,5.583V31.86a3.583,3.583,0,0,1-3.583,3.583H7.583A3.583,3.583,0,0,1,4,31.86V5.583A3.583,3.583,0,0,1,7.583,2h21.5A3.583,3.583,0,0,1,32.666,5.583ZM14.75,28.277a3.583,3.583,0,1,0-3.583,3.583A3.583,3.583,0,0,0,14.75,28.277Zm0-9.555a3.583,3.583,0,1,0-3.583,3.583A3.583,3.583,0,0,0,14.75,18.722Zm0-9.555a3.583,3.583,0,1,0-3.583,3.583A3.583,3.583,0,0,0,14.75,9.167ZM26.694,28.874a.6.6,0,0,0-.6-.6H17.736a.6.6,0,0,0,0,1.194H26.1A.6.6,0,0,0,26.694,28.874Zm0-2.389a.6.6,0,0,0-.6-.6H17.736a.6.6,0,1,0,0,1.194H26.1A.6.6,0,0,0,26.694,26.486Zm0-7.167a.6.6,0,0,0-.6-.6H17.736a.6.6,0,1,0,0,1.194H26.1A.6.6,0,0,0,26.694,19.319Zm0-2.389a.6.6,0,0,0-.6-.6H17.736a.6.6,0,0,0,0,1.194H26.1A.6.6,0,0,0,26.694,16.93Zm0-7.167a.6.6,0,0,0-.6-.6H17.736a.6.6,0,0,0,0,1.194H26.1A.6.6,0,0,0,26.694,9.764Zm0-2.389a.6.6,0,0,0-.6-.6H17.736a.6.6,0,0,0,0,1.194H26.1A.6.6,0,0,0,26.694,7.375Z"
      transform="translate(-4 -2)"
      fill={fill}
    />
  </svg>
);

export default DocumentListIcon;
