import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";

type ViolationImageModalProps = {
  isOpen: boolean;
  imageUrl: string;
  onClose: () => void;
};

const ViolationImageModal: React.FC<ViolationImageModalProps> = ({
  isOpen,
  imageUrl,
  onClose,
}) => {
  // const [zoomLevel, setZoomLevel] = useState(0.75);

  // useEffect(() => {
  //   if (isOpen) {
  //     document.body.style.overflow = "hidden"; 
  //   } else {
  //     document.body.style.overflow = "unset"; // allow scrolling
  //     setZoomLevel(0.5); 
  //   }
  // }, [isOpen]);


  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80">
      <div className="relative w-full max-w-2xl mx-4 bg-white rounded-xl overflow-hidden">
        {/* Header with controls */}
        <div className="flex justify-between items-center p-4 bg-gray-50 border-b">
          <h3 className="text-lg font-semibold text-gray-800">
            تصویر خلافی خودرو
          </h3>
          <div className="">
           
           

            <button
              onClick={onClose}
              className=" rounded-b-full bg-gradient-to-t from-gray-200 to-transparent text-gray-700 w-10 h-10 pt-2 flex justify-center items-center hover:opacity-80 text-base font-bold"
              title="بستن"
            >
              &times;
            </button>
          </div>
        </div>

        {/* Image container */}
        <div className="h-[60vh] relative w-full overflow-auto">
          <div className="flex items-center justify-center h-full w-full p-4">
            <Image
              src={imageUrl}
              alt="Violation Image"
              width={500}
              height={300}
              className="object-cover transition-transform duration-300"
              // style={{ transform: `scale(${zoomLevel})` }}
            />
          </div>
        </div>

        {/* Instructions */}
        <div className="p-4 bg-gray-50 border-t text-center flex justify-center">
          <a
              href={imageUrl}
              download={`violation_image_${new Date().getTime()}.jpg`}
              className="p-2 rounded-lg bg-blue-600 !text-center text-white block !w-40 max-md:w-full hover:bg-blue-700 "
              title="دانلود تصویر"
            >
              دانلود
            </a>
        </div>
      </div>
    </div>
  );
};

export default ViolationImageModal;
