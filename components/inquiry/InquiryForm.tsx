"use client";

import { useForm, useWatch } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../UI/form";
import {
  violationInquiryFormSchema,
  ViolationInquiryType,
  ViolationTypeEnum,
} from "@/lib/types/zod-schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import useInquiryPost from "@/lib/hooks/useInquiryPost";
import { useRouter } from "nextjs-toploader/app";
import {
  CAR_PLATE_ALPHABET,
  CAR_PLATE_LEFT,
  CAR_PLATE_MIDDLE,
  CAR_PLATE_RIGHT,
  MOTOR_PLATE_LEFT,
  MOTOR_PLATE_RIGHT,
  NATIONAL_CODE_MAX_LENGTH,
  PHONE_NUMBER_MAX_LENGTH,
} from "@/lib/constants";
import {
  isValidIranianNationalCode,
  plateNumberIsNotValid,
} from "@/lib/validations";
import toast from "react-hot-toast";
import CustomInput from "@/components/UI/CustomInput";
import Card from "@/components/common/Card";
import ChoiceItem from "@/components/common/ChoiceItem";
import InquiryHeader from "@/components/inquiry/InquiryHeader";
import CustomButton from "@/components/UI/CustomButton";
import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import PlateInputMotor, {
  motorPlatePartsMaxLengths,
} from "@/components/UI/MotorPlateInput";
import PlateInputCar, {
  carPlatePartsMaxLengths,
  PlateInputRef,
} from "@/components/UI/CarPlateInput";
import IdCardIcon from "@/components/common/svg/IdCardIcon";
import MobileInputIcon from "@/components/common/svg/MobileInputIcon";
import InquiryCarIcon from "@/components/common/svg/InquiryCarIcon";
import InquiryMotorIcon from "@/components/common/svg/InquiryMotorIcon";
import { useEffect, useRef, useState } from "react";
import StatusMessage from "@/components/common/StatusMessage";
import { ServiceStatusType } from "@/lib/types/types";
import CheckoutProgress from "../shop/checkout/CheckoutProgress";

type Props = {
  // queryParams: ViolationQueryParams
  isMotor: boolean;
  withDetails?: boolean;
  status: ServiceStatusType;
  boxTitle?: string;
};

const CAR = "خودرو";
const MOTOR_CYCLE = "موتور سیکلت";

export default function InquiryFormContainer({
  isMotor,
  withDetails,
  status,
  boxTitle,
}: Props) {
  const [key, setKey] = useState(0);

  useEffect(() => {
    setKey((pre) => pre + 1);
  }, [isMotor, withDetails]);

  return (
    <InquiryForm
      key={key}
      isMotor={isMotor}
      withDetails={withDetails}
      status={status}
      boxTitle={boxTitle}
    />
  );
}

export function InquiryForm({ isMotor, withDetails, status, boxTitle }: Props) {
  // const {left, right, middle, alphabet, withDetails, nationalCode, phoneNumber} = queryParams
  const { mutate, isLoading } = useInquiryPost();
  const router = useRouter();
  const vehicleType = isMotor ? MOTOR_CYCLE : CAR;
  const nationalCodeRef = useRef<HTMLInputElement | null>(null);
  const phoneNumberRef = useRef<HTMLInputElement | null>(null);
  const plateInputRef = useRef<PlateInputRef | null>(null);

  const form = useForm<ViolationInquiryType>({
    mode: "onSubmit",
    resolver: zodResolver(violationInquiryFormSchema),
    defaultValues: {
      plateNumber: isMotor ? ["", ""] : ["", "", "", ""],
      type: withDetails
        ? ViolationTypeEnum.WITH_INFO
        : ViolationTypeEnum.WITHOUT_INFO,
      nationalCode: undefined,
      phoneNumber: undefined,
    },
  });

  const isWithInfo = form.getValues("type") === ViolationTypeEnum.WITH_INFO;
  const detailTypeText = isWithInfo ? "با جزییات" : "بدون جزییات";
  const watchType = useWatch({ control: form.control, name: "type" });

  useEffect(() => {
    const plateNumber = form.getValues("plateNumber");
    if (watchType === ViolationTypeEnum.WITHOUT_INFO) {
      form.reset({
        type: ViolationTypeEnum.WITHOUT_INFO,
        plateNumber,
        nationalCode: undefined,
        phoneNumber: undefined,
      });
    }
    if (
      (!isMotor &&
        plateNumber[CAR_PLATE_RIGHT] &&
        plateNumber[CAR_PLATE_RIGHT].length ===
          carPlatePartsMaxLengths[CAR_PLATE_RIGHT]) ||
      (isMotor &&
        plateNumber[MOTOR_PLATE_RIGHT] &&
        plateNumber[MOTOR_PLATE_RIGHT].length ===
          motorPlatePartsMaxLengths[MOTOR_PLATE_RIGHT])
    ) {
      return;
    }
    plateInputRef.current?.focus();
  }, [watchType]);

  useEffect(() => {
    const subscription = form.watch((values) => {
      if (!isWithInfo) return;

      const plateNumber = values.plateNumber;
      const nationalCode = values.nationalCode;
      const phoneNumber = values.phoneNumber;

      if (
        !nationalCode &&
        ((!isMotor &&
          plateNumber?.[CAR_PLATE_RIGHT]?.length ===
            carPlatePartsMaxLengths[CAR_PLATE_RIGHT]) ||
          (isMotor &&
            plateNumber?.[MOTOR_PLATE_RIGHT]?.length ===
              motorPlatePartsMaxLengths[MOTOR_PLATE_RIGHT]))
      ) {
        nationalCodeRef.current?.focus();
      }

      if (
        document.activeElement === nationalCodeRef.current &&
        !phoneNumber &&
        nationalCode?.length === NATIONAL_CODE_MAX_LENGTH
      ) {
        phoneNumberRef.current?.focus();
      }
    });

    return () => subscription.unsubscribe(); // Cleanup on unmount
  }, [isMotor, isWithInfo]); // `watch` reference is stable, so this won't cause extra re-renders.

  async function onSubmit(values: ViolationInquiryType) {
    if (isLoading || status === "DISABLED" || status === "COMING_SOON") return;

    if (plateNumberIsNotValid(values.plateNumber, isMotor)) {
      form.setError("plateNumber", { message: "شماره پلاک را صحیح وارد کنید" });
      return;
    }

    const plaque = form.getValues("plateNumber");
    const phone = form.getValues("phoneNumber");
    const nationalId = form.getValues("nationalCode");

    if (nationalId) {
      const isNationalCodeValid = isValidIranianNationalCode(nationalId);
      if (!isNationalCodeValid) {
        form.setError("nationalCode", { message: "کد ملی را صحیح وارد کنید" });
        return;
      }
    }
    const mutateResult = await mutate({
      withDetails: isWithInfo ? "true" : "false",
      phoneNumber: isWithInfo ? phone : "",
      nationalCode: isWithInfo ? nationalId : "",
      inquiry: "true",
      middle: isMotor ? "" : plaque[CAR_PLATE_MIDDLE],
      left: isMotor ? plaque[MOTOR_PLATE_LEFT] : plaque[CAR_PLATE_LEFT],
      right: isMotor ? plaque[MOTOR_PLATE_RIGHT] : plaque[CAR_PLATE_RIGHT],
      alphabet: isMotor ? "" : plaque[CAR_PLATE_ALPHABET],
      isMotor: isMotor ? "true" : "false",
      reInquiry: "false",
    });

    if (!mutateResult.success && mutateResult.href) {
      router.push(mutateResult.href);
    } else if (!mutateResult.success && mutateResult.message) {
      toast.error(mutateResult.message, {
        duration: 20000,
      });
    } else if (mutateResult.success && mutateResult.href) {
      router.push(mutateResult.href);
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mt-5 md:mt-10"
        autoComplete="off"
      >
        {withDetails === undefined && (
          <Card className="!p-5">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="w-full flex items-center justify-normal gap-x-2">
                      <div className="flex-1">
                        <ChoiceItem
                          label="استعلام با جزییات"
                          checked={ViolationTypeEnum.WITH_INFO === field.value}
                          value={ViolationTypeEnum.WITH_INFO}
                          name={field.name}
                          onClick={(value) => field.onChange(value)}
                        />
                      </div>
                      <div className="flex-1">
                        <ChoiceItem
                          label="استعلام بدون جزییات"
                          checked={
                            ViolationTypeEnum.WITHOUT_INFO === field.value
                          }
                          value={ViolationTypeEnum.WITHOUT_INFO}
                          name={field.name}
                          onClick={(value) => field.onChange(value)}
                        />
                      </div>
                    </div>
                  </FormControl>
                </FormItem>
              )}
            />
          </Card>
        )}
        <Card className="!px-0 !pt-5 !pb-10  mt-5">
          {/* <div className="w-full flex justify-center mb-6 md:px-10 px-3  items-start ">
            <div className="flex w-full max-w-4xl items-baseline justify-between h-full">
              <div className="flex items-center flex-1">
                <div className="flex flex-col items-center text-center w-full">
                  <div className=" flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white text-sm font-bold">
                    1
                  </div>
                  <p className="mt-2 text-xs font-medium text-gray-700">
                    اطلاعات خودرو
                  </p>
                </div>
                <div className="flex-1 h-[2px] bg-gray-300 ml-2"></div>
              </div>

              <div className="flex items-center flex-1">
                <div className="flex flex-col items-center text-center w-full">
                  <div className="step-line relative flex items-center justify-center w-8 h-8 rounded-full bg-gray-50 text-gray-500 text-sm font-bold">
                    2
                  </div>
                  <p className="mt-2 text-xs font-medium text-gray-700">
                    شارژ کیف پول
                  </p>
                </div>
                <div className="flex-1 h-[2px] bg-gray-300 ml-2"></div>
              </div>

              <div className="flex items-center flex-1">
                <div className="flex flex-col items-center text-center w-full">
                  <div className="step-line relative flex items-center justify-center w-8 h-8 rounded-full bg-gray-50 text-gray-500 text-sm font-bold">
                    3
                  </div>
                  <p className="mt-2 text-xs font-medium text-gray-700">
                    مشاهده لیست خلافی‌ها
                  </p>
                </div>
                <div className="flex-1 h-[2px] bg-gray-300 ml-2"></div>
              </div>

              <div className="flex items-center flex-1">
                <div className="flex flex-col items-center text-center w-full">
                  <div className="step-line relative flex items-center justify-center w-8 h-8 rounded-full bg-gray-50 text-gray-500 text-sm font-bold">
                    4
                  </div>
                  <p className="mt-2 text-xs font-medium text-gray-700">
                    مشاهده تصویر خلافی
                  </p>
                </div>
              </div>
            </div>
          </div> */}

          <InquiryHeader
            icon={
              isMotor ? (
                <InquiryMotorIcon width={50} height={50} />
              ) : (
                <InquiryCarIcon width={50} height={50} />
              )
            }
            // title={`استعلام خلافی ${vehicleType} ${detailTypeText}`}
            title={boxTitle || `استعلام خلافی ${vehicleType} ${detailTypeText}`}
          />
          <div className="px-5 mt-2 md:px-14">
            <FormField
              control={form.control}
              name="plateNumber"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    {isMotor ? (
                      <PlateInputMotor {...field} ref={plateInputRef} />
                    ) : (
                      <PlateInputCar {...field} ref={plateInputRef} />
                    )}
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />
            <div className="mt-8">
              {form.getValues("type") === ViolationTypeEnum.WITH_INFO && (
                <>
                  <div>
                    <FormField
                      control={form.control}
                      name="nationalCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#596068] text-xs">{`کد ملی مالک ${vehicleType}`}</FormLabel>
                          <FormControl>
                            <CustomInput
                              variant="secondary"
                              allowOnlyNumbers
                              maxLength={NATIONAL_CODE_MAX_LENGTH}
                              placeholder="کد ملی مالک را وارد کنید"
                              {...field}
                              ref={nationalCodeRef}
                              leftIcon={<IdCardIcon height={20} width={20} />}
                              direction="rtl"
                              inputMode="numeric"
                              autoFocus
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="mt-4">
                    <FormField
                      control={form.control}
                      name="phoneNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#596068] text-xs">{`تلفن همراه مالک ${vehicleType}`}</FormLabel>
                          <FormControl>
                            <CustomInput
                              variant="secondary"
                              allowOnlyNumbers
                              maxLength={PHONE_NUMBER_MAX_LENGTH}
                              leftIcon={
                                <MobileInputIcon width={15} height={15} />
                              }
                              placeholder="شماره موبایل مالک را وارد کنید"
                              {...field}
                              ref={phoneNumberRef}
                              inputMode="numeric"
                              direction="rtl"
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />
                  </div>
                </>
              )}

              <div className="mt-5">
                <ChoiceWrapper backgroundColor="#FFF5D8" borderColor="#F7BC06">
                  <div className="flex py-3 flex-col gap-y-1">
                    <p className="text-[#5E646B] text-sm">
                      هزینه استعلام: 16,170 تومان
                    </p>
                    <p className="text-[#5E646B] text-sm">
                      خودراکس هیچ دخل و تصرفی در تعیین این هزینه ندارد
                    </p>
                  </div>
                </ChoiceWrapper>
              </div>

              <CustomButton
                loading={isLoading}
                disabled={
                  isLoading || status === "DISABLED" || status === "COMING_SOON"
                }
                type="submit"
                className="mt-5 !py-5"
              >
                استعلام
              </CustomButton>
              {(status === "DISABLED" || status === "COMING_SOON") && (
                <div className="mt-5">
                  <StatusMessage status={status} />
                </div>
              )}
            </div>
          </div>
        </Card>
      </form>
    </Form>
  );
}
