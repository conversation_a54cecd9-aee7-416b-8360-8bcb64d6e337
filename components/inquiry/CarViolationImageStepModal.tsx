"use client";

import { useState, useEffect } from "react";

const CarViolationImageStepModal: React.FC = () => {
  const [showContent, setShowContent] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(true); 

  useEffect(() => {
    
    setTimeout(() => setShowContent(true), 50);
  }, []);

  const closeModal = () => {
    setShowContent(false);
    setTimeout(() => setIsOpen(false), 300); 
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      
      <div
        onClick={closeModal}
        className={`absolute inset-0 bg-black/50 transition-opacity duration-300 ${
          showContent ? "opacity-100" : "opacity-0"
        }`}
      />

      
      <div
        className={`relative z-10 w-[90%] max-w-md rounded-2xl bg-white p-6 shadow-lg transform transition-all duration-300 ${
          showContent
            ? "opacity-100 scale-100 translate-y-0"
            : "opacity-0 scale-95 translate-y-5"
        }`}
      >
        
        <h2 className="text-lg font-semibold text-gray-800 mb-4">
          📌 روند مشاهده خلافی و تصاویر
        </h2>

       
        <div className="space-y-3 text-sm text-gray-700">
          <div className="flex items-start gap-2">
            <span className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-white text-xs font-bold">
              1
            </span>
            <p>اطلاعات خودرو را وارد کنید</p>
          </div>

          <div className="flex items-start gap-2">
            <span className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-white text-xs font-bold">
              2
            </span>
            <p>
              پرداخت <b>16,170 تومان</b> برای مشاهده لیست خلافی‌ها
            </p>
          </div>

          <div className="flex items-start gap-2">
            <span className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-white text-xs font-bold">
              3
            </span>
            <p>
              پرداخت <b>۳,۷۹۵ تومان</b> برای مشاهده تصویر هر خلافی{" "}
              <span className="text-red-500">(در صورت وجود)</span>
            </p>
          </div>
        </div>

        
        <button
          onClick={closeModal}
          className="mt-6 w-full rounded-xl bg-blue-600 px-4 py-2 text-white text-sm font-medium shadow hover:bg-blue-700 transition-colors"
        >
          فهمیدم، ادامه بده
        </button>
      </div>
    </div>
  );
};

export default CarViolationImageStepModal;
