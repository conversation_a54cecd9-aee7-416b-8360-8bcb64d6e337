'use client';

import dynamic from 'next/dynamic';
import { ServiceStatusType } from '@/lib/types/types';
import React from 'react';

const InquiryFormContainer = dynamic(() => import('./InquiryForm'), {
  loading: () => <div className='h-[50vh]'></div>,
  ssr: false,
});

type Props = {
  isMotor: boolean;
  withDetails?: boolean;
  status: ServiceStatusType;
  boxTitle?: string;
};

export default function InquiryFormClientWrapper({ isMotor, withDetails, status, boxTitle }: Props) {
  return (
    <InquiryFormContainer boxTitle={boxTitle} isMotor={isMotor} withDetails={withDetails} status={status} />
  );
}
