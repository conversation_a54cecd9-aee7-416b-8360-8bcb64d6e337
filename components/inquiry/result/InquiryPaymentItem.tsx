'use client';
import { useEffect, useRef } from 'react';
import clsx from 'clsx';

type PaymentItemProps = {
    selected: string | null;
    onSelect: (id: string) => void;
    amount: string
};

export default function InquiryPaymentItem({ selected, onSelect, amount }: PaymentItemProps) {
    const onlineRef = useRef<HTMLInputElement | null>(null);
    const installmentRef = useRef<HTMLInputElement | null>(null);

    const isOnlineChecked = selected === "online";

    useEffect(() => {
        if (onlineRef.current) {
            onlineRef.current.checked = isOnlineChecked;
        }
    }, [isOnlineChecked]);

    return (
        <div className="flex flex-col gap-2 h-full">
            {/* گزینه پرداخت آنلاین */}
            <div
                onClick={() => onSelect("online")}
                className={clsx(
                    "mt-3 text-gray-400 p-3 rounded-xl border cursor-pointer transition-all duration-300",
                    isOnlineChecked
                        ? "border-[#3c53c7]  scale-[1.01] bg-white"
                        : "border-gray-200 hover:border-[#3c53c7]"
                )}
            >
                <div className="flex justify-between items-center">
                    <div className='flex gap-5 items-center'>
                        <div className="checkbox-wrapper-15">
                            <input
                                ref={onlineRef}
                                className="inp-cbx"
                                id="cbx-online"
                                type="checkbox"
                                style={{ display: "none" }}
                                readOnly
                            />
                            <label className="cbx" htmlFor="cbx-online">
                                <span>
                                    <svg width="12px" height="9px" viewBox="0 0 12 9">
                                        <polyline points="1 5 4 8 11 1"></polyline>
                                    </svg>
                                </span>
                            </label>
                        </div>
                        <div className="flex flex-col items-start gap-1">
                            <h4 className="max-md:text-sm text-gray-700 font-bold">پرداخت آنلاین</h4>
                            <p className="text-sm max-md:text-xs text-gray-500">
                                پرداخت از طریق درگاه بانکی
                            </p>
                        </div>
                    </div>
                    <div>
                        <p className='text-gray-700 font-bold'>
                            {amount.toLocaleString()} تومان
                        </p>
                    </div>
                </div>
            </div>

            {/* گزینه پرداخت اقساطی (غیرفعال) */}
            <div
                className={clsx(
                    " text-gray-400 p-3 rounded-xl border transition-all duration-300  cursor-not-allowed",
                    "border-gray-200 bg-white"
                )}
            >
                <div className="flex gap-5 items-center">
                    <div className="checkbox-wrapper-15">
                        <input
                            ref={installmentRef}
                            className="inp-cbx "
                            id="cbx-installment"
                            type="checkbox"
                            style={{ display: "none" }}
                            disabled
                        />
                        <label className="cbx " htmlFor="cbx-installment ">
                            <span className='!border-gray-200'>
                                <svg width="12px" height="9px" viewBox="0 0 12 9">
                                    <polyline points="1 5 4 8 11 1"></polyline>
                                </svg>
                            </span>
                        </label>
                    </div>
                    <div className="flex flex-col gap-1">
                        <h4 className="max-md:text-sm text-gray-600">پرداخت اقساطی</h4>
                        <p className="text-sm max-md:text-xs text-gray-400">
                            (به زودی فعال می‌شود)
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
}
