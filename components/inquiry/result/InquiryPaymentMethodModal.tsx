import MailboxIcon from '@/components/common/svg/MailboxIcon';
import InquiryPaymentItem from './InquiryPaymentItem';
import { PaymentDetail } from '@/lib/types/action-types';
import { X } from 'lucide-react';
import React, { useEffect, useState } from 'react';


const InquiryPaymentMethodModal = ({ detail, selected, setSelected, setIsDialogOpen, paymentUrl }: {
    detail: string;
    selected: string;
    setSelected: (id: string) => void;
    setIsDialogOpen: (isOpen: boolean) => void;
    paymentUrl: string;
}) => {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        const timer = setTimeout(() => setIsVisible(true), 80); // 120ms delay
        return () => clearTimeout(timer);
    }, []);

    return (
        <div
            onClick={(e) => {
                e.stopPropagation();
                setIsDialogOpen(false);
            }}
            className="fixed inset-0 z-[9999999] bg-black bg-opacity-30 flex md:items-center items-end justify-center "
        >
            <div
                onClick={(e) => e.stopPropagation()}
                className={`bg-white p-6 md:rounded-xl max-w-xl w-full text-center shadow-xl transform transition-all duration-300 delay-100
                    ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-20 opacity-0'}
                `}
                style={{ willChange: 'transform, opacity' }}
            >
                <div className="flex justify-between items-center">
                    <div className="flex gap-3 items-center">
                        <div className="p-3 bg-gradient-to-t from-gray-100 rounded-b-full">
                            <MailboxIcon />
                        </div>
                        <h2 className="text-lg font-bold">جزئیات پرداخت خلافی</h2>
                    </div>
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            setIsDialogOpen(false);
                        }}
                        className="bg-gradient bg-gray-50 rounded-full p-2 hover:bg-gray-100 border border-gray-200"
                    >
                        <X size={20} />
                    </button>
                </div>
                <div className='min-h-80 mt-8'>
                    <InquiryPaymentItem amount={detail} selected={selected} onSelect={setSelected} />
                </div>
                <div className="flex gap-5 items-center mt-8">
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            setIsDialogOpen(false);
                        }}
                        className="bg-gray-200 rounded-xl py-3 px-4 text-sm w-1/2"
                    >
                        انصراف
                    </button>
                    <button disabled={paymentUrl == "#"} onClick={() => window.open(paymentUrl, "_blank")} className="bg-primary hover:bg-blue-600 transition-all rounded-xl py-3 px-4 text-sm w-1/2 text-white">
                        تایید و ادامه پرداخت
                    </button>
                </div>
            </div>
        </div>
    );
};

export default InquiryPaymentMethodModal;