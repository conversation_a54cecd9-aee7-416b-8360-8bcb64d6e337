'use client'

import ChoiceWrapper from "@/components/common/ChoiceWrapper";
import CameraIcon from "@/components/common/svg/CameraIcon";
import CustomButton from "@/components/UI/CustomButton";
import SubtractIcon from "@/components/common/svg/SubtractIcon";
import ResultDetailsUpIcon from "@/components/common/svg/ResultDetailsUpIcon";
import ResultDetailsLeftIcon from "@/components/common/svg/ResultDetailsLeftIcon";
import { useState } from "react";
import { cn } from "@/lib/utils";
import SubtractBorderIcon from "@/components/common/svg/SubtractBorderIcon";
import { PaymentDetail } from "@/lib/types/action-types";
import { getViolationImageAction } from "@/actions/inquiry.action";
import toast from "react-hot-toast";
import DialogModal from "@/components/common/DialogModal";
import ViolationImageModal from "../ViolationImageModal";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/UI/dialog";
import { Mailbox, X } from "lucide-react";
import MailboxIcon from "@/components/common/svg/MailboxIcon";
import PaymentItem from "@/components/shop/checkout/PaymentItem";
import InquiryPaymentItem from "./InquiryPaymentItem";
import InquiryPaymentMethodModal from "./InquiryPaymentMethodModal";

type Props = {
    isCollapse?: boolean;
    detail: PaymentDetail
}


export default function InquiryResultWithDetails({ detail, isCollapse = true }: Props) {

    const [loading, setLoading] = useState(false);
    const [violationImageUrl, setViolationImageUrl] = useState<string | null>(null);
    const [isImageModalOpen, setIsImageModalOpen] = useState(false);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [selected, setSelected] = useState<string>("online");


    const [collapse, setCollapse] = useState(isCollapse)
    const handleViolationImage = async () => {
        setLoading(true);
        try {
            const response = await getViolationImageAction(detail.unique_id);
            const imageUrl = response?.data?.Parameters?.VehicleImageUrl;

            if (response.status === 200 && imageUrl) {
                setViolationImageUrl(imageUrl);
                setIsImageModalOpen(true);
            } else {
                toast.error(response?.data?.message || "تصویری برای نمایش یافت نشد.");
            }
        } catch {
            toast.error("خطا در دریافت تصویر.");
        } finally {
            setLoading(false);
        }
    };
    return (
        <>
            {loading && (
                <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
                    <div className="w-10 h-10 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
                </div>
            )}

            <ChoiceWrapper
                backgroundColor='#F9FAFB'
                borderColor={collapse ? "#EEEEEE" : "#CCCACA"}
                className={collapse ? 'border-solid !border' : ''}
            >
                <div
                    onClick={() => setCollapse(!collapse)}
                    className='relative  w-full cursor-pointer'>
                    <div className={cn('w-full p-2 pt-5 pb-10 max-h-[410px] duration-200 transition-all relative', {
                        "max-h-[115px]": collapse
                    })}>
                        <div className='absolute bottom-[-7px] left-[-13px]'>
                            {collapse ? <SubtractBorderIcon width={100} height={100} /> :
                                <SubtractIcon width={100} height={100} />}
                        </div>
                        <div className='absolute bottom-[-13px] left-[22px] cursor-pointer'
                            onClick={() => setCollapse((prev) => !prev)}>
                            {
                                !collapse ? (<ResultDetailsUpIcon width={30} height={30} />) : (<ResultDetailsLeftIcon />)
                            }
                        </div>
                        <div className='w-full flex justify-between items-center gap-x-1'>
                            <span>
                                <p className='text-[#000000]'>{detail.amount} <span className='text-xs'>تومان</span></p>
                                <p className='text-xs mt-1'>{detail.type}</p>
                            </span>
                            <CameraIcon fill={detail.has_image ? "#1F84FB" : "#9DA5B0"} height={40} width={40} />
                        </div>
                        {collapse && (<div className='mt-2 flex items-center gap-1 text-[#9DA5B0] text-xs'>
                            <span>تاریخ وقوع:</span>
                            <span>{detail.date_time}</span>
                        </div>)}
                        <div className={cn('mt-5 flex flex-col items-center gap-5', {
                            "hidden": collapse,
                        })}>
                            <div className='w-full flex justify-between items-center text-[#596068] text-xs'>
                                <span>تاریخ وقوع:</span>
                                <span>{detail.date_time}</span>
                            </div>
                            {/*<div className='w-full flex justify-between items-center text-[#596068]'>*/}
                            {/*    <span className='text-xs'>روش ثبت:</span>*/}
                            {/*    <span className='text-xs'>{detail.}</span>*/}
                            {/*</div>*/}
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>محل وقوع:</span>
                                <span className='text-xs'>{detail.location}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>شناسه قبض:</span>
                                <span className='text-xs'>{detail.bill_id}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>شناسه پرداخت:</span>
                                <span className='text-xs'>{detail.payment_id}</span>
                            </div>
                            <div className='w-full flex justify-between items-center text-[#596068]'>
                                <span className='text-xs'>شماره ریال جریمه:</span>
                                <span className='text-xs'>{detail.serial_number}</span>
                            </div>
                            <div className="w-full flex flex-col items-center gap-2">
                                <CustomButton
                                    onClick={(e) => {
                                        e.stopPropagation()
                                        setIsDialogOpen(true)
                                        // window.open(detail.payment_url, "_blank");
                                    }}
                                    bgColor='' className="py-4" {...detail.payment_url == "#" ? { disabled: true } : {}}>پرداخت خلافی </CustomButton>
                                {
                                    isDialogOpen && (
                                        // <div
                                        //     onClick={(e) => {
                                        //         e.stopPropagation();
                                        //         setIsDialogOpen(false);
                                        //     }}
                                        //     className="fixed inset-0 z-[9999] bg-black bg-opacity-30 flex items-center justify-center  max-md:px-3"
                                        // >
                                        //     <div
                                        //         onClick={(e) => e.stopPropagation()}
                                        //         className="bg-white p-6 rounded-xl max-w-xl w-full text-center shadow-xl"
                                        //     >
                                        //         <div className="flex justify-between items-center">
                                        //             <div className="flex gap-3 items-center">
                                        //                 <div className="p-3 bg-gradient-to-t from-gray-100 rounded-b-full">
                                        //                     <MailboxIcon />
                                        //                 </div>
                                        //                 <h2 className="text-lg font-bold">جزئیات پرداخت خلافی</h2>
                                        //             </div>
                                        //             <button
                                        //                 onClick={(e) => {
                                        //                     e.stopPropagation();
                                        //                     setIsDialogOpen(false);
                                        //                 }}
                                        //                 className="bg-gradient bg-gray-50 rounded-full p-2 hover:bg-gray-100 border border-gray-200"
                                        //             >
                                        //                 <X size={20} />
                                        //             </button>
                                        //         </div>
                                        //         <div>
                                        //             <InquiryPaymentItem amount={detail.amount} selected={selected} onSelect={setSelected} />
                                        //         </div>
                                        //         <div className="flex gap-5 items-center mt-8">
                                        //             <button
                                        //                 onClick={(e) => {
                                        //                     e.stopPropagation();
                                        //                     setIsDialogOpen(false);
                                        //                 }}
                                        //                 className="bg-gray-200 rounded-xl py-3 px-4 text-sm w-1/2"
                                        //             >
                                        //                 انصراف
                                        //             </button>
                                        //             <button className="bg-primary rounded-xl py-3 px-4 text-sm w-1/2 text-white">
                                        //                 تایید و ادامه پرداخت
                                        //             </button>
                                        //         </div>
                                               
                                        //     </div>
                                        // </div>
                                        <InquiryPaymentMethodModal paymentUrl={detail.payment_url}  detail={detail.amount} selected={selected} setSelected={setSelected} setIsDialogOpen={setIsDialogOpen} />
                                    )
                                }
                                {
                                    detail.has_image ?
                                        <DialogModal
                                            title={"دریافت تصویر خلافی خودرو"}
                                            onConfirm={handleViolationImage}
                                            description={
                                                "هزینه استعلام برای هر تصویر خلافی 3,795 تومان است و فقط یکبار از حساب شما کسر می‌شود. برای دفعات بعدی دریافت همان تصویر، هزینه‌ای مجدد پرداخت نخواهید کرد. آیا می‌خواهید ادامه دهید؟"
                                            }
                                        >
                                            <CustomButton className="py-4 flex flex-col text-xs w-full bg-green-600">
                                                دریافت تصویر خلافی
                                            </CustomButton>
                                        </DialogModal>

                                        : ''

                                }
                            </div>
                        </div>

                    </div>

                </div>
            </ChoiceWrapper>

            {isImageModalOpen && violationImageUrl && (
                <ViolationImageModal
                    isOpen={isImageModalOpen}
                    imageUrl={violationImageUrl}
                    onClose={() => {
                        setIsImageModalOpen(false);
                        setViolationImageUrl(null);
                    }}
                />
            )}
        </>
    );
}
