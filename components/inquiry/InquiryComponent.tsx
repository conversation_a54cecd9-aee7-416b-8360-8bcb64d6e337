"use client";
import Container from "@/components/common/Container";
import { PageDescription } from "@/components/common/PageDescription";
import "@/styles/styles.css";
// import InquiryFormClientWrapper from '@/components/inquiry/InquiryFormClientWrapper';
const InquiryFormClientWrapper = dynamic(
  () => import("@/components/inquiry/InquiryFormClientWrapper"),
  {
    loading: () => <div className="h-[50vh]"></div>,
    ssr: false,
  }
);
import { ServiceStatusType } from "@/lib/types/types";
import dynamic from "next/dynamic";

type Props = {
  isMotor: boolean;
  withDetails?: boolean;
  title: string;
  status?: ServiceStatusType;
  boxTitle?: string;
};

export default function InquiryComponent({
  isMotor,
  withDetails,
  title,
  status = "ACTIVE",
  boxTitle,
}: Props) {
  return (
    <Container className="mb-16">
      <div className="w-full max-w-[653px]">
        <PageDescription
          title={title}
          description="اطلاعات خود را وارد کرده تا از وضعیت پرداختی‌های خود مطلع شوید."
        />

    
        <InquiryFormClientWrapper
          isMotor={isMotor}
          withDetails={withDetails}
          status={status}
          boxTitle={boxTitle}
        />
      </div>
    </Container>
  );
}
