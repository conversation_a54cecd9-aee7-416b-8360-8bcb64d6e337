"use server"


import { apiClient } from "@/lib/apiClient";
import CookieService from "@/lib/services/cookie-service";
import { getCarIdDocumentsData } from "@/lib/types/action-types";
import { getUserAddresses } from "@/lib/utils";
import { cookies } from "next/headers";
// import { cookies } from "next/headers";

export const getServicesAction = async () => {
    const response = await (await apiClient(`services`, {
        next: { revalidate: 900 }
    })).json()
    return response

}

export async function SetNaghlieCookie(token: string) {
    try {
        console.log('this is runnin');

        await CookieService.setAuthorizationToken(token)



    } catch (error) {
        console.log('<<<<<<<<<<<<<<<<<<<<<<<<<<<<<');

        console.log(error);


    }
    // const {setAuthorizationToken} = CookieService

}

/**
 * Send notification token to the server
 * @param token Firebase notification token
 * @param path Current path when the token was generated
 * @param status Accept or reject status
 * @param userAgent User's browser and device information
 * @param ip User's IP address (optional, will be determined server-side if not provided)
 * @returns Response from the API
 */
export async function sendNotificationToken(
    token: string,
    path: string,
    status: string,
    userAgent: string = '',
    ip: string = ''
) {
    try {

        const response = await apiClient("notification/client-token", {
            method: "POST",
            body: {
                token,
                path,
                app: "khodrox",
                status,
                agent: userAgent,
                ip
            }
        });

        const data = await response.json();
        return { success: response.ok, data };
    } catch (error) {
        console.error("Error sending notification token:", error);
        return { success: false, error: "Failed to send notification token" };
    }
}

// export async function trackUTMFromServer(fullURL: string, u_trace: string) {
//   try {
//     // 1. خواندن کوکی‌ها از ریکوئست (در سرور)
//     const cookieStore = cookies();
//     const allCookies: Record<string, string> = {};

//     (await cookieStore).getAll().forEach((cookie) => {
//       allCookies[cookie.name] = cookie.value;
//     });

//     // 2. ارسال به API خارجی
//     // const response = await fetch("https://dl.khodrox.com/api/v1/utm-conversion", {
//     //   method: "POST",
//     //   headers: {
//     //     "Content-Type": "application/json",
//     //     "UTM-DATA": JSON.stringify(allCookies || null),
//     //     "Authorization": `Bearer ${await CookieService.getAuthorizationToken()}`
//     //   },
//     //   body: JSON.stringify({
//     //     utm: fullURL,
//     //     page: fullURL,
//     //   }),
//     //   cache: "no-store",
//     // });
//     const response = await apiClient("utm-conversion", {
//       method: "POST",
//       body: {
//         utm: fullURL,
//         page: fullURL,
//         u_trace
//       },
//       headers: {
//         "UTM-DATA": JSON.stringify(allCookies || null),
//       },
//       next: { revalidate: 0 },
//     });

//     const data = await response.json();
//     console.log(data);
    
//     // 3. اگر داده utm دریافت شد، به عنوان کوکی برای کاربر ذخیره کنیم
//     if (data?.data?.utm) {
//       const expires = new Date(Date.now() + 2 * 24 * 60 * 60 * 1000); // 2 روز

//       for (const [key, value] of Object.entries(data.data.utm)) {
//         (await cookies()).set({
//           name: key,
//           value: String(value),
//           path: "/",
//           expires,
//         });
//       }
//     }

//     return { success: true, data };
//   } catch (err) {
//     console.error("Server UTM tracking error:", err);
//     return { success: false, error: "Something went wrong" };
//   }
// }

export async function trackUTMFromServer(fullURL: string, u_trace: string, localStorageData: Record<string,string>) {
  try {
    const response = await apiClient("utm-conversion", {
      method: "POST",
      body: {
        utm: fullURL,
        page: fullURL,
        u_trace,
        localStorage: localStorageData
      }
    });

    return await response.json();
  } catch (err) {
    console.error("Server UTM tracking error:", err);
    return { success: false, error: "Something went wrong" };
  }
}

export async function getCarIdDocumentsInquiryLogin(data: getCarIdDocumentsData) {
    console.log("data:", data);
    try {
        const response = await apiClient(`user/inquiry/licenses/status`, {
            method: "POST",
            body: data,
        })
        return await response.json()
    } catch (error) {
        console.error("Error getting car ID documents:", error);
        return { success: false, error: "Failed to get car ID documents" };
    }
}