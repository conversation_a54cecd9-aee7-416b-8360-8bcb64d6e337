'use server'

import {
    ActionResult,
    CarInsuranceData,
    CarPaymentDetails,
    DrivingLicensePointType,
    getCarIdDocumentsData,
    getDrivingStatusData,
    HistoryTransaction,
    InquireCarArgs,
    InquireCarResponse,
} from "@/lib/types/action-types";
import {handleActionErrorResponse} from "@/utils/helpers";
import inquiryService from "@/lib/services/inquiry.service";
import {apiClient} from "@/lib/apiClient";
import {cookies} from "next/headers";

export async function postInquireVehicle(args: InquireCarArgs): Promise<ActionResult<InquireCarResponse>> {
    try {
        const responseResult = await inquiryService.inquireVehicle<InquireCarResponse>({payload: args});
        console.log("++++++++++++++++++", responseResult);
        return {
            success: true,
            data: responseResult.data,
        }

    } catch (error: any) {
        console.log("-----------------------", error);
        return handleActionErrorResponse(error)
    }
}

export async function getVehicleInquiry(traceNumber: string): Promise<ActionResult<CarPaymentDetails>> {
    try {
        const responseResult = await inquiryService.getInquiry<CarPaymentDetails>(traceNumber)
        return {
            success: true,
            data: responseResult.data,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function getInquiryHistory(): Promise<ActionResult<HistoryTransaction[]>> {
    try {
        const responseResult = await inquiryService.getInquiryHistory<HistoryTransaction[]>()
        return {
            success: true,
            data: responseResult.data,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function getViolationImageAction(id: string) {
    try {
        const cookieStore = await cookies();
        const accessToken = cookieStore.get("authorization")?.value;
        const response = await apiClient(`user/inquiry/picture`, {
            method: "POST",
            headers: {Authorization: `${accessToken}`},
            body: {inquiryId: id},
        })

        return await response.json()
    } catch (error: unknown) {
        console.log(error);
        return { success: false, error: "Failed to get violation image" };
    }
}

export async function getDrivingLicensePoint(data: DrivingLicensePointType) {
    try {
        const response = await apiClient('user/inquiry/certificate/negative-points', {
            method: "POST",
            body: {
                details: data
            },
        })
        return await response.json()
    } catch (error) {
        console.error("Error getting driving license point:", error);
        return { success: false, error: "Failed to get driving license point" };
    }
}
export async function getDrivingLicensePointByTrace(trace: string) {
    try {
        const response = await apiClient(`user/inquiry/certificate/${trace}/negative-points`, {
            method: "GET",
        })
        return await response.json()
    } catch (error) {
        console.error("Error getting driving license point:", error);
        return { success: false, error: "Failed to get driving license point" };
    }
}

export async function getCarIdDocumentsInquiry(data: getCarIdDocumentsData) {
    console.log("data:", data);
    try {
        const response = await apiClient(`user/inquiry/licenses/status`, {
            method: "POST",
            body: data,
        })
        return await response.json()
    } catch (error) {
        console.error("Error getting car ID documents:", error);
        return { success: false, error: "Failed to get car ID documents" };
    }
}

export async function getCarIdDocumentsByTrace(trace: string) {
    try {
        const response = await apiClient(`user/inquiry/licenses/${trace}/status`, {
            method: "GET",
        })
        return await response.json()
    } catch (error) {
        console.error("Error getting car ID documents by trace:", error);
        return { success: false, error: "Failed to get car ID documents by trace" };
    }
}

export async function getDrivingStatusInquiry(data: getDrivingStatusData) {
    console.log("data:", data);
    try {
        const response = await apiClient(`user/inquiry/driving/status`, {
            method: "POST",
            body: {
                details: data
            },
        })
        return await response.json()
    } catch (error) {
        console.error("Error getting driving status:", error);
        return { success: false, error: "Failed to get driving status" };
    }
}


export async function getDrivingLicenseStatusByTrace(trace: string) {
    try {
        const response = await apiClient(`user/inquiry/driving/${trace}/status`, {
            method: "GET",
        })
        return await response.json()
    } catch (error) {
        console.error("Error getting driving license status:", error);
        return { success: false, error: "Failed to get driving license status" };
    }
}

export async function getCarInsuranceInquiry(data: CarInsuranceData) {
    console.log("data:", data);
    try {
        const response = await apiClient(`user/inquiry/insurance/status`, {
            method: "POST",
            body: data,
        })
        return await response.json()
    } catch (error) {
        console.error("Error getting car insurance:", error);
        return { success: false, error: "Failed to get car insurance" };
    }
}

export async function getCarInsuranceByTrace(trace: string) {
    try {
        const response = await apiClient(`user/inquiry/insurance/${trace}/status`, {
            method: "GET",
        })
        return await response.json()
    } catch (error) {
        console.error("Error getting car insurance by trace:", error);
        return { success: false, error: "Failed to get car insurance by trace" };
    }
}