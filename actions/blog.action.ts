'use server'

import {ActionResult, ArticleResponse} from "@/lib/types/action-types";
import {handleActionErrorResponse} from "@/utils/helpers";
import blogService from "@/lib/services/blog.service";
import { apiClient } from "@/lib/apiClient";

export async function getBlogPosts(): Promise<ActionResult<ArticleResponse[]>> {
    try {
        const responseResult = await blogService.getBlogPosts<ArticleResponse[]>()
        return {
            success: true,
            data: responseResult.data,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}
export async function getBlogPostsByPage(page: string | number) {
    try {
        const response = await apiClient(`articles?page=${page}`, {
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting blog posts:", error);
        return { success: false, error: "Failed to get blog posts" };
    }
}
