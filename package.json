{"name": "khodrox", "version": "0.2.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "generate-firebase-config": "node scripts/generate-firebase-config.js", "predev": "npm run generate-firebase-config", "prebuild": "npm run generate-firebase-config", "analyze": "set ANALYZE=true&& next build"}, "dependencies": {"@hookform/resolvers": "^4.1.2", "@neshan-maps-platform/ol": "^1.0.5", "@neshan-maps-platform/react-openlayers": "^3.0.2", "@next/bundle-analyzer": "15.5.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-slot": "^1.1.2", "@tanstack/react-query": "^5.77.1", "@types/leaflet": "^1.9.19", "@types/ol": "^5.3.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.7.1", "http-status-codes": "^2.3.0", "jalaali-js": "^1.2.8", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "next": "15.5.2", "next-pwa": "^5.6.0", "nextjs-toploader": "^3.8.16", "rc-slider": "^11.1.8", "react": "19.1.1", "react-dom": "19.1.1", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-leaflet": "^5.0.0-rc.2", "react-multi-date-picker": "^4.5.2", "react-spinners": "^0.15.0", "save-dev": "^0.0.1-security", "swiper": "^11.2.5", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@fullhuman/postcss-purgecss": "^7.0.2", "@types/jalaali-js": "^1.2.0", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "19.1.12", "@types/react-dom": "19.1.9", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.5.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "overrides": {"@types/react": "19.1.12", "@types/react-dom": "19.1.9"}}