# Khodrox - Modern Web Application

Khodrox is a modern web application built with Next.js, React, and Tailwind CSS. The application appears to be a comprehensive platform for vehicle-related services, including a blog, e-commerce functionality, and various inquiry services.

## Table of Contents

- [Features](#features)
- [Tech Stack](#tech-stack)
- [Folder Structure](#folder-structure)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
- [Component Documentation](#component-documentation)
  - [Blog Components](#blog-components)
  - [Inquiry Components](#inquiry-components)
  - [Common Components](#common-components)
  - [UI Components](#ui-components)
  - [Header Components](#header-components)
  - [Dashboard Components](#dashboard-components)
  - [Wallet Components](#wallet-components)
  - [Payment Components](#payment-components)
  - [Shop Components](#shop-components)
- [Firebase Configuration](#firebase-configuration)
  - [Overview](#overview)
  - [Configuration Files](#configuration-files)
  - [Service Worker](#service-worker)
  - [Notification Components](#notification-components)
    - [Firebase Notification Service](#firebase-notification-service)
    - [SendNotification Component](#sendnotification-component)
      - [Key Features](#key-features)
      - [Component Structure](#component-structure)
      - [Implementation Details](#implementation-details)
      - [Usage](#usage)
      - [Customization](#customization)
      - [Firebase Message Payload](#firebase-message-payload)
      - [Sending Test Notifications](#sending-test-notifications)
      - [Handling Notifications](#handling-notifications)
  - [Server Actions for Notifications](#server-actions-for-notifications)
  - [Best Practices and Advanced Usage](#best-practices-and-advanced-usage)
  - [Setup for New Developers](#setup-for-new-developers)
  - [Production Deployment](#production-deployment)
  - [Troubleshooting](#troubleshooting)
- [Cart Context System](#cart-context-system)
  - [Overview](#overview-1)
  - [Architecture](#architecture-1)
  - [State Management](#state-management-1)
  - [Key Functions](#key-functions)
  - [Data Flow](#data-flow)
  - [Error Handling](#error-handling-1)
  - [Usage Examples](#usage-examples)
  - [Types and Interfaces](#types-and-interfaces)
  - [Dependencies](#dependencies)
  - [Best Practices](#best-practices-1)

## Features

- **Blog System**: A comprehensive blog with categories, search functionality, and article recommendations.
- **E-commerce**: Product listings, checkout process, and payment integration.
- **User Dashboard**: User account management and service history.
- **Vehicle Services**: Various vehicle-related services including:
  - Car and motor violation tickets inquiry.
  - Driving license status.
  - Vehicle documentation.
  - Insurance services.

## Tech Stack

- **Framework**: Next.js (App Router).
- **UI Library**: React.
- **Styling**: Tailwind CSS.
- **Form Handling**: React Hook Form with Zod validation.
- **State Management**: React Context API.
- **Icons**: Lucide React.
- **Notifications**: React Hot Toast.
- **Push Notifications**: Firebase Cloud Messaging (FCM).

## Folder Structure

```
khodrox/
├── actions/                  # Server actions for data fetching and mutations
├── app/                      # Next.js app router pages
│   ├── (blog)/               # Blog-related pages
│   ├── (dashboard)/          # User dashboard pages
│   ├── (khodrox)/            # Main service pages
│   │   ├── (inquiry)/        # Inquiry service pages
│   │   └── ...               # Other service pages
│   └── (shop)/               # E-commerce pages
├── components/               # React components
│   ├── blog/                 # Blog-related components
│   │   └── SinglePage/       # Blog post page components
│   ├── common/               # Shared/common components
│   │   └── svg/              # SVG icon components
│   ├── Dashboard/            # Dashboard components
│   ├── Header/               # Header components
│   ├── inquiry/              # Inquiry form components
│   │   └── result/           # Inquiry result components
│   ├── InquiryStaticComponents/ # Static components for inquiry pages
│   ├── shop/                 # E-commerce components
│   ├── UI/                   # UI components (buttons, inputs, etc.)
│   └── Wallet/               # Wallet and payment components
├── lib/                      # Utility functions, hooks, and types
│   ├── constants/            # Application constants
│   ├── firebase/             # Firebase configuration
│   ├── hooks/                # Custom React hooks
│   ├── notification/         # Notification services
│   ├── types/                # TypeScript type definitions
│   └── utils.ts              # Utility functions
├── public/                   # Static assets
│   ├── assets/
│   │   └── images/           # Image assets
│   ├── firebase-config.js    # Generated Firebase configuration
│   └── firebase-messaging-sw.js # Firebase service worker
├── scripts/                  # Utility scripts
└── styles/                   # Global styles
```

## Getting Started

### Prerequisites

- Node.js (v18 or later).
- npm or yarn.

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/khodrox.git
   cd khodrox
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit the .env file with your configuration values
   ```

4. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## Component Documentation

### Blog Components

The blog system includes various components for displaying blog posts, categories, and related content:

- `BlogHead`: Displays the blog post header with title and metadata.
- `BlogPostCategory`: Shows the blog post categories.
- `BlogPostTableOfContent`: Displays a table of contents for the blog post.
- `RecommendedPost`: Shows a recommended blog post with image and title.
- `BlogSearchBox`: Provides search functionality for the blog.
- `ArticleSlider`: Displays a slider of related articles.
- `NewArticlesItem`: Shows a preview of a new article.

### Inquiry Components

The inquiry system allows users to search for various vehicle-related information:

- `InquiryComponent`: Main component for inquiry forms.
- `InquiryForm`: Form component for submitting inquiries.
- `InquiryResultWithDetails`: Displays inquiry results with detailed information.

### Common Components

These components are shared across multiple parts of the application:

- `Container`: A layout wrapper component with background styling.
- `Card`: A reusable card component with consistent styling.
- `ChoiceWrapper`: A selectable container for choice-based UI elements.
- `PageDescription`: Component for displaying page titles and descriptions.
- `ServiceIcon`: SVG icon component for various services.
- `SendNotification`: Component for requesting notification permissions.

### UI Components

Reusable UI components that provide consistent styling across the application:

- `Button`: Standard button component with various styles and variants.
- `CustomButton`: Extended button component with additional features like loading state.
- `Input`: Standardized input component with consistent styling.
- `Pagination`: Component for handling pagination in lists.
- `Footer`: Application footer with links and information.

### Header Components

Components related to the application header and navigation:

- `Navbar`: Main navigation component for the application.
- `ShopHeader`: Specialized header for the shop section.
- `ShopNavbar`: Navigation specifically for the shop section.
- `ServiceList`: Displays available services in the header.
- `CategoryMenu`: Dropdown menu for product categories.
- `UserLoggedInButton`: Button displayed when a user is logged in.
- `DashboardNavbar`: Navigation for the dashboard section.

### Dashboard Components

Components for the user dashboard interface:

- `UserQuickAccess`: Quick access tiles for common user actions.
- `WalletAmount`: Displays the user's wallet balance.
- `PopularServices`: Shows popular services in the dashboard.
- `UserLastInquiries`: Displays the user's recent inquiries.
- `FavoritesList`: Shows the user's favorite items.
- `LastOrders`: Displays the user's recent orders.

### Shop Components

Components related to the shop and e-commerce functionality:

- `ProductCartInfo`: Displays product information in cart context.
- `ProductCartDetails`: Parent component managing cart state and variants.
- `ProviderShopInfo`: Shows provider/shop information for products.
- `CartPopup`: Interactive cart popup with item management controls.

### Wallet Components

Components related to the wallet and payment functionality:

- `WalletBox`: Main container for wallet information.
- `WalletBalance`: Displays the current wallet balance.
- `PaymentBox`: Interface for adding funds to the wallet.
- `PaymentResult`: Shows the result of a payment transaction.

### Payment Components

Components for handling payment processing and displaying payment results:

- `PaymentStatusHeader`: Displays payment success/failure status with visual indicators.
- `PaymentDetails`: Shows detailed payment information in a structured format.
- `PaymentDetailRow`: Reusable component for displaying individual payment details.

#### Qabzino Payment Callback Page

The Qabzino payment callback page (`app/(khodrox)/payment/qabzino/callback/page.tsx`) handles the verification and display of payment results after a user completes a payment through the Qabzino payment gateway. This page processes the payment verification response and presents the results in a user-friendly format.

**Key Features:**
- Processes payment verification API responses.
- Displays payment status (success/failure) with appropriate visual indicators.
- Shows detailed payment information including amount, bill ID, and transaction details.
- Implements a clean, visually appealing UI with consistent styling.
- Responsive design for both desktop and mobile devices.

## Firebase Configuration

### Overview

The application uses Firebase Cloud Messaging (FCM) for push notifications. This section explains how the Firebase integration works and how to set it up for development or production.

### Configuration Files

#### 1. Environment Variables

Firebase configuration is stored in the `.env` file:

```
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id
NEXT_PUBLIC_FIREBASE_VAPID_KEY=your-vapid-key
```

#### 2. Firebase Config Module

Located at `lib/firebase/config.ts`, this file exports the Firebase configuration with fallbacks:

```typescript
export const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "fallback-api-key",
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "fallback-auth-domain",
  // other config values
};
```

### Service Worker

The service worker handles background notifications and is located at `public/firebase-messaging-sw.js`:

```javascript
importScripts('https://www.gstatic.com/firebasejs/10.12.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.12.1/firebase-messaging-compat.js');
importScripts('/firebase-config.js');

firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

messaging.onBackgroundMessage(function(payload) {
  console.log('Received background message: ', payload);
});
```

### Notification Components

#### Firebase Notification Service

Located at `lib/notification/FirebaseNotificationService.js`, this service handles Firebase messaging:

```javascript
export class FirebaseNotificationService {
  constructor(config, vapidKey) {
    this.config = config;
    this.vapidKey = vapidKey;
  }

  async init() {
    const app = initializeApp(this.config);
    this.messaging = getMessaging(app);
    await Notification.requestPermission();
  }

  async getToken() {
    return await getToken(this.messaging, { vapidKey: this.vapidKey });
  }

  onForegroundMessage(callback) {
    onMessage(this.messaging, callback);
  }
}
```

### Best Practices and Advanced Usage

#### Storing the FCM Token

After obtaining the FCM token, you should send it to your server to associate it with the user:

```typescript
const token = await notifService.getToken();
await fetch('/api/users/me/notification-token', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({ token }),
});
```

#### Handling Token Refresh

Firebase may refresh the token periodically. You should handle this by setting up a token refresh listener:

```typescript
firebase.messaging().onTokenRefresh(async () => {
  try {
    const refreshedToken = await firebase.messaging().getToken();
    console.log('Token refreshed:', refreshedToken);
    await updateTokenOnServer(refreshedToken);
  } catch (error) {
    console.error('Unable to retrieve refreshed token:', error);
  }
});
```

#### Handling Notification Clicks

You can handle notification clicks to navigate to specific pages:

```javascript
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  const data = event.notification.data;
  if (data && data.url) {
    clients.openWindow(data.url);
  } else {
    clients.openWindow('/');
  }
});
```

#### Segmenting Users for Targeted Notifications

You can use Firebase Cloud Messaging topics to send notifications to specific user segments:

```typescript
async function subscribeToTopic(token, topic) {
  await fetch('/api/notifications/subscribe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ token, topic }),
  });
}
```

#### Testing Notifications in Development

For testing notifications during development:

1. Use the Firebase Console to send test messages.
2. Create a simple API endpoint to send notifications:

```typescript
import { NextApiRequest, NextApiResponse } from 'next';
import admin from 'firebase-admin';

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\n/g, '\n'),
    }),
  });
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { token, title, body } = req.body;
    await admin.messaging().send({
      token,
      notification: {
        title,
        body,
      },
    });
    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error sending notification:', error);
    return res.status(500).json({ error: 'Failed to send notification' });
  }
}
```

#### Implementing a Notification Preference Center

Consider adding a notification preference center to allow users to manage their notification settings:

```tsx
function NotificationPreferences() {
  const [preferences, setPreferences] = useState({
    marketing: true,
    updates: true,
    reminders: false,
  });

  const handleToggle = (key) => {
    setPreferences((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <div className="notification-preferences">
      <h2>Notification Preferences</h2>
      <div className="preference-item">
        <label>Marketing Notifications</label>
        <Switch checked={preferences.marketing} onChange={() => handleToggle('marketing')} />
      </div>
    </div>
  );
}
```

## Cart Context System

### Overview

The Cart Context (`lib/context/cart-context.tsx`) is a comprehensive state management system that handles all shopping cart functionality in the Khodrox application. It provides a centralized way to manage cart items, synchronize data between localStorage and the backend API, and maintain consistent cart state across the entire application.

**Key Features:**
- **Dual Storage**: Seamlessly manages cart data in both localStorage (for guests) and backend API (for authenticated users)
- **Real-time Synchronization**: Automatically syncs cart changes with the server and updates pricing
- **Optimistic Updates**: Provides immediate UI feedback while API calls are processed in the background
- **Error Handling**: Robust error handling with automatic rollback on API failures
- **Loading States**: Comprehensive loading indicators for better user experience

### Architecture

The cart system integrates tightly with the user authentication system and follows a hybrid approach:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Components    │    │  Cart Context   │    │   Backend API   │
│                 │◄──►│                 │◄──►│                 │
│ - CartPopup     │    │ - State Mgmt    │    │ - Persistence   │
│ - ProductCart   │    │ - Sync Logic    │    │ - Calculations  │
│ - Checkout      │    │ - Error Handle  │    │ - Validation    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  localStorage   │
                       │                 │
                       │ - Guest Carts   │
                       │ - Offline Data  │
                       └─────────────────┘
```

**Integration with Authentication:**
- **Guest Users**: Cart data is stored in localStorage only
- **Authenticated Users**: Cart data is synchronized with the backend API
- **Login Transition**: When a guest logs in, their localStorage cart is automatically migrated to the server

### State Management

The cart context manages several key state variables that work together to provide a complete shopping cart experience:

#### Core State Variables

| Variable | Type | Description |
|----------|------|-------------|
| `cartItems` | `CartItem[]` | Array of items currently in the cart |
| `cartResponse` | `CartApiResponse` | Complete API response containing server-calculated totals |
| `isLoading` | `boolean` | Initial loading state when cart is being fetched |
| `isInitialized` | `boolean` | Whether the cart has completed initial setup |
| `isUpdating` | `boolean` | Loading state for cart operations (add/remove/update) |
| `isUserContextInitialized` | `boolean` | Whether user authentication context is ready |

#### Calculated Values

These values are derived from the state and automatically update when cart data changes:

| Variable | Type | Source | Description |
|----------|------|--------|-------------|
| `totalItems` | `number` | Calculated | Total quantity of all items in cart |
| `finalPrice` | `number` | `cartResponse.data.total` | Final price after all discounts and taxes |
| `totalPrice` | `number` | `cartResponse.data.subtotal` | Original price before discounts |
| `totalDiscount` | `number` | `cartResponse.data.total_discount` | Total amount saved through discounts |

#### State Flow

```typescript
// Initial State
cartItems: []
isLoading: true
isInitialized: false

// After Authentication Check
isUserContextInitialized: true

// After Cart Load (Guest)
cartItems: [items from localStorage]
isLoading: false
isInitialized: true

// After Cart Load (Authenticated)
cartItems: [items from API]
cartResponse: { data: { total, subtotal, total_discount } }
finalPrice: cartResponse.data.total
isLoading: false
isInitialized: true

// During Cart Operations
isUpdating: true
// ... optimistic UI updates ...
// ... API call ...
// ... refresh cart data ...
isUpdating: false
```

### Key Functions

The cart context provides several functions for managing cart items. Understanding when and how to use each function is crucial for proper implementation.

#### Adding Items to Cart

##### `addToCart(variant, quantity?, productInfo?)`

**Purpose**: Add a new product variant to the cart from product pages
**Use Case**: Product detail pages, quick add buttons
**Parameters**:
- `variant: Variation` - Complete product variant object with all details
- `quantity?: number` - Number of items to add (default: 1)
- `productInfo?: { image?: string; name?: string }` - Additional product metadata

```typescript
// Example: Adding a product from product detail page
const { addToCart } = useCart();

const handleAddToCart = () => {
  addToCart(selectedVariant, 2, {
    image: product.galleries[0]?.url,
    name: product.title
  });
};
```

##### `checkoutAddToCart(product)`

**Purpose**: Increase quantity of existing cart items during checkout process
**Use Case**: Cart popup, checkout page quantity controls
**Parameters**:
- `product: { id: string, quantity: number }` - Minimal product identifier and quantity

```typescript
// Example: Increasing quantity in cart popup
const { checkoutAddToCart } = useCart();

const handleIncreaseQuantity = (productId: string) => {
  checkoutAddToCart({ id: productId, quantity: 1 });
};
```

**Key Differences**:
- `addToCart`: Creates new cart entries, includes full product metadata
- `checkoutAddToCart`: Only modifies existing items, minimal data required

#### Removing/Decreasing Items

##### `decreaseFromCart(variant)`

**Purpose**: Decrease quantity or remove items using full variant object
**Use Case**: Product pages, detailed cart views
**Parameters**:
- `variant: Variation` - Complete variant object

##### `checkoutDecreaseFromCart(productId)`

**Purpose**: Decrease quantity during checkout process
**Use Case**: Cart popup, checkout page controls
**Parameters**:
- `productId: string` - Simple product identifier

```typescript
// Example: Decrease quantity in cart popup
const { checkoutDecreaseFromCart } = useCart();

const handleDecreaseQuantity = (productId: string) => {
  checkoutDecreaseFromCart(productId);
};
```

#### Other Cart Operations

##### `removeFromCart(variantId)`

**Purpose**: Completely remove an item from cart
**Parameters**:
- `variantId: string` - Variant identifier

##### `updateQuantity(variantId, newQuantity)`

**Purpose**: Set exact quantity for a cart item
**Parameters**:
- `variantId: string` - Variant identifier
- `newQuantity: number` - New quantity value

##### `clearCart()`

**Purpose**: Remove all items from cart
**Use Case**: After successful checkout, user logout

### Data Flow

The cart system follows a sophisticated data flow pattern that ensures consistency between UI, local storage, and backend API:

#### 1. Optimistic Updates Pattern

```typescript
// Step 1: Immediate UI Update
setCartItems(prevItems => {
  // Update UI immediately for better UX
  return updatedItems;
});

// Step 2: API Synchronization
if (isLoggedIn) {
  const response = await apiCall();

  if (!response.success) {
    // Step 3: Rollback on Failure
    setCartItems(prevItems => {
      // Revert to previous state
      return revertedItems;
    });
  } else {
    // Step 4: Refresh from Server
    const updatedCart = await getUserCart();
    setCartResponse(updatedCart);
    setCartItems(updatedCart.data?.items || []);
  }
}
```

#### 2. Data Synchronization Flow

```
User Action (Add/Remove Item)
         │
         ▼
   Optimistic UI Update
         │
         ▼
    API Call (if logged in)
         │
    ┌────▼────┐
    │ Success │
    └────┬────┘
         │
         ▼
   Refresh Cart Data
         │
         ▼
   Update finalPrice
         │
         ▼
    UI Reflects Server State

    ┌─────────┐
    │ Failure │
    └────┬────┘
         │
         ▼
   Rollback UI Changes
         │
         ▼
   Show Error Message
```

#### 3. Storage Strategy

| User State | Primary Storage | Backup Storage | Sync Trigger |
|------------|----------------|----------------|--------------|
| Guest | localStorage | None | On each change |
| Authenticated | Backend API | localStorage | On login/change |
| Login Transition | Backend API | localStorage | Immediate migration |

### Error Handling

The cart context implements comprehensive error handling to maintain data consistency and provide good user experience:

#### 1. API Failure Recovery

```typescript
// Automatic rollback on API failures
if (!response.success) {
  setCartItems(prevItems => {
    const existingItem = prevItems.find(item => item.id === productId);
    if (existingItem) {
      // Revert quantity change
      return prevItems.map(item =>
        item.id === productId
          ? { ...item, quantity: item.quantity - addedQuantity }
          : item
      );
    }
    return prevItems;
  });
  console.error('Failed to add item to cart via API');
}
```

#### 2. Network Error Handling

- **Offline Mode**: Cart continues to work with localStorage when API is unavailable
- **Retry Logic**: Failed API calls are logged for potential retry mechanisms
- **Graceful Degradation**: UI remains functional even when server sync fails

#### 3. Data Consistency Checks

- **Server Refresh**: After successful API operations, cart data is refreshed from server
- **Price Validation**: Server-calculated prices always override client-side calculations
- **Inventory Validation**: Server validates product availability and stock levels

### Usage Examples

#### Basic Component Integration

```typescript
import { useCart } from '@/lib/context/cart-context';

function ProductCard({ product }) {
  const { addToCart, isUpdating, cartItems } = useCart();

  // Check if product is already in cart
  const isInCart = cartItems.some(item => item.id === product.default_variant.id);

  const handleAddToCart = () => {
    addToCart(product.default_variant, 1, {
      image: product.galleries[0]?.url,
      name: product.title
    });
  };

  return (
    <div className="product-card">
      <h3>{product.title}</h3>
      <p>{product.default_variant.price.toLocaleString()} تومان</p>

      <button
        onClick={handleAddToCart}
        disabled={isUpdating || isInCart}
        className={`btn ${isInCart ? 'btn-success' : 'btn-primary'}`}
      >
        {isUpdating ? 'در حال افزودن...' : isInCart ? 'در سبد خرید' : 'افزودن به سبد'}
      </button>
    </div>
  );
}
```

#### Cart Popup Component

```typescript
import { useCart } from '@/lib/context/cart-context';

function CartPopup({ onClose }) {
  const {
    cartItems,
    finalPrice,
    totalItems,
    checkoutAddToCart,
    checkoutDecreaseFromCart,
    isUpdating
  } = useCart();

  return (
    <div className="cart-popup">
      <h3>سبد خرید ({totalItems} محصول)</h3>

      {cartItems.map(item => (
        <div key={item.id} className="cart-item">
          <img src={item.image} alt={item.name} />
          <div className="item-details">
            <h4>{item.name}</h4>
            <p>{item.price.toLocaleString()} تومان</p>
          </div>

          <div className="quantity-controls">
            <button
              onClick={() => checkoutDecreaseFromCart(item.id)}
              disabled={isUpdating}
            >
              -
            </button>
            <span>{item.quantity}</span>
            <button
              onClick={() => checkoutAddToCart({ id: item.id, quantity: 1 })}
              disabled={isUpdating}
            >
              +
            </button>
          </div>
        </div>
      ))}

      <div className="cart-total">
        <strong>مجموع: {finalPrice.toLocaleString()} تومان</strong>
      </div>

      <button className="checkout-btn">
        ادامه خرید
      </button>
    </div>
  );
}
```

#### Loading States Handling

```typescript
function CartStatus() {
  const { isLoading, isInitialized, isUpdating, totalItems } = useCart();

  if (isLoading || !isInitialized) {
    return <div>در حال بارگذاری سبد خرید...</div>;
  }

  return (
    <div className="cart-status">
      <span>سبد خرید ({totalItems})</span>
      {isUpdating && <span className="loading-indicator">در حال بروزرسانی...</span>}
    </div>
  );
}
```

### Types and Interfaces

#### Core Types

```typescript
// Main cart item interface
interface CartApiItem {
  variant?: Variation;           // Complete product variant data
  quantity: number;              // Item quantity in cart
  attributes?: CartItemAttribute[]; // Product attributes (color, size, etc.)
  id?: string;                   // Variant identifier
  image?: string;                // Product image URL
  name?: string;                 // Product name
  sale_price: number | null;     // Discounted price (if applicable)
  price: number;                 // Original price
  product_id?: string;           // Parent product identifier
  created_at?: string;           // Creation timestamp
  updated_at?: string;           // Last update timestamp
  [key: string]: unknown;        // Additional dynamic properties
}

// API response structure
interface CartApiResponse {
  success: boolean;              // Operation success status
  data?: {
    items?: CartApiItem[];       // Array of cart items
    total?: number;              // Final price after discounts
    total_discount?: number;     // Total discount amount
    user_id?: string;            // User identifier
    subtotal?: number;           // Price before discounts
  };
  message: string;               // Response message
  status: number;                // HTTP status code
  error?: string;                // Error message (if any)
}

// Product variation interface
interface Variation {
  id: string;                    // Unique variant identifier
  sku: string;                   // Stock keeping unit
  price: number;                 // Base price
  sale_price: number;            // Sale price
  current_quantity: number;      // Available stock
  size: string;                  // Product size
  color: string;                 // Product color
}

// Cart item attributes
interface CartItemAttribute {
  type: string;                  // Attribute type (color, size, etc.)
  title: string;                 // Display title
  value: string;                 // Attribute value
  extra_data: {                  // Additional metadata
    [key: string]: any;
  } | null;
}
```

### Dependencies

The cart context relies on several other systems and contexts:

#### Required Dependencies

1. **Authentication Context** (`useAuth`)
   - Provides `isLoggedIn` status
   - Supplies `userData` for user identification
   - Triggers cart migration on login/logout

2. **Cart Actions** (`actions/cart.action.ts`)
   - `getUserCart()` - Fetch cart from API
   - `addToCard()` - Add item to cart via API
   - `decreaseFromCartAction()` - Remove/decrease item via API
   - `addMultipleToCart()` - Bulk add items to cart

3. **Product Types** (`lib/types/product.types.ts`)
   - `Variation` interface
   - `CartItemAttribute` interface

#### Context Hierarchy

```typescript
// Proper context nesting
function App() {
  return (
    <AuthProvider>          {/* Must be parent of CartProvider */}
      <CartProvider>        {/* Depends on auth state */}
        <YourAppComponents />
      </CartProvider>
    </AuthProvider>
  );
}
```

### Best Practices

#### 1. Function Selection Guidelines

| Scenario | Recommended Function | Reason |
|----------|---------------------|---------|
| Product detail page | `addToCart()` | Includes full product metadata |
| Cart popup controls | `checkoutAddToCart()` / `checkoutDecreaseFromCart()` | Optimized for existing items |
| Bulk operations | `addMultipleToCart()` | Efficient for multiple items |
| Complete removal | `removeFromCart()` | Clear intent, immediate removal |

#### 2. Loading State Management

```typescript
// Always handle loading states
const { isLoading, isInitialized, isUpdating } = useCart();

// Don't render cart UI until initialized
if (!isInitialized) {
  return <CartSkeleton />;
}

// Disable interactions during updates
<button disabled={isUpdating}>
  {isUpdating ? 'Processing...' : 'Add to Cart'}
</button>
```

#### 3. Error Handling

```typescript
// Monitor cart operations for errors
useEffect(() => {
  // Listen for cart errors and show user feedback
  // The context handles rollbacks automatically
}, []);
```

#### 4. Performance Optimization

- **Memoize expensive calculations** when using cart data
- **Use loading states** to prevent multiple simultaneous API calls
- **Debounce rapid quantity changes** in UI components

#### 5. Data Consistency

- **Always use server-calculated prices** (`finalPrice`) for checkout
- **Refresh cart data** after successful operations
- **Handle edge cases** like item removal during checkout

#### 6. Security Considerations

- **Never trust client-side calculations** for final pricing
- **Validate cart contents** on the server before checkout
- **Implement proper authentication** for cart API endpoints