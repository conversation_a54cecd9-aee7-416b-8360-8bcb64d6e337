import DrivingLicenseComponent from '@/components/DrivingLicensePoint/DrivingLicenseComponent'
import envConfig from "@/lib/config-env";
import Faq from '@/components/InquiryStaticComponents/Faq';
import ChildSchema from '@/components/common/ChildSchema';
import { PageContentResponse } from '@/lib/types/types';
import ArticleSection from '@/components/InquiryStaticComponents/ArticleSection';
import { getPageContent } from '@/lib/utils';
import DrivingLicenseStatusWrapper from '@/components/DrivingLicenseStatus/DrivingLicenseStatusWrapper';
import BlogComments from '@/components/blog/SinglePage/BlogComments';


export async function generateMetadata() {
    const data = await getPageContent("driving-license-status");
    
    return {
        title: data.meta_title,
        description: data.meta_description,
        keywords: data.tags || [],
    ...(data.meta_search && { robots: data.meta_search }),
    ...(data.canonical && { alternates: { canonical: data.canonical } }),
    };
}

export const revalidate = 0

const DrivingLicensePage = async () => {
    const env = envConfig()
    const status = env.Services.LICENSE_DRIVER_STATUS_SECTION
    const data: PageContentResponse = await getPageContent("driving-license-status")
    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id='driving-license-status'
                    schema={schema}
                />
            }
            {/* <DrivingLicenseComponent title={title || ""} status={status} /> */}
            <DrivingLicenseStatusWrapper title={title || ""} status={status} />
            {
                description &&
                <ArticleSection description={description} />
            }
            {/* <DrivingLicenseStatusAbout /> */}
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
            <div className="md:max-w-7xl mx-auto mb-10">
                  {/* <UserComments /> */}
                  <BlogComments contentId={data.id} />
            </div>
        </>
    )
}

export default DrivingLicensePage