import Container from '@/components/common/Container'
import Image from 'next/image'
import BimeHero from "@/public/assets/images/bime-hero.webp"
import PlateForm from '@/components/Insurance/Main/PlateForm'
import Approval<PERSON><PERSON> from '@/components/common/svg/Approval<PERSON><PERSON>'
import DocumentListIcon from '@/components/common/svg/DocumentListIcon'
import SupportIcon from '@/components/common/svg/SupportIcon'
import HelpOperatorIcon from '@/components/common/svg/HelpoperatorIcon'
import Faq from '@/components/InquiryStaticComponents/Faq'
import { getPageContent } from '@/lib/utils'
import { PageContentResponse, ServiceColorVariantType, ServiceStatusType } from '@/lib/types/types'
import type { Metadata } from "next";
import DoubleQoute from "@/public/assets/images/double-qoute.png"
import { BadgePercent, ReceiptText, Star, Umbrella } from 'lucide-react'
import MoneyBagIcon from '@/components/common/svg/MoneyBagIcon'
import WithdrawIcon from '@/components/common/svg/WithdrawIcon'
import DisplayServiceIcon from '@/components/Services/DisplayServiceIcon'
import Link from 'next/link'
import ServiceIcon from '@/components/common/svg/services/ServiceIcon'
import { CAR_TICKETS_PATH, MOTOR_TICKETS_PATH, CAR_VIOLATION_IMAGE_PATH, CAR_INSURANCE_PATH, DRIVING_LICENSE_STATUS_PATH, CAR_ID_DOCUMENTS_PATH, DRIVING_LICENSE_POINT_PATH, PLATE_HISTORY_PATH } from '@/lib/routes'
import Customer1 from "@/public/assets/images/customer-1.png"
import Customer2 from "@/public/assets/images/customer-2.png"
import Iran from "@/public/assets/images/IRAN.webp"
import Dana from "@/public/assets/images/dana.webp"
import Moalem from "@/public/assets/images/MOALLEM.webp"
import Novin from "@/public/assets/images/novin.webp"
import Pasargad from "@/public/assets/images/PASARGAD.webp"
import Saman from "@/public/assets/images/SAMAN.webp"
import Sarmad from "@/public/assets/images/SARMAD.webp"
import Taavon from "@/public/assets/images/TAAVON.webp"
import HomeBlogSection from '@/components/blog/HomeBlogSection'


export const metadata: Metadata = {
    robots: {
        index: false,
        follow: false,
    }
};

interface ServiceItemProps {
    href: string;
    name: string;
    icon: React.ReactNode;
    colorVariant?: ServiceColorVariantType;
    status: ServiceStatusType;
    type: "car" | "motor"
}

const ServiceItem: React.FC<ServiceItemProps> = ({ href, name, icon, colorVariant, status }) => {

    const isDisabled = status === 'DEACTIVE'
    return (
        <li className="w-full">
            {isDisabled ? (
                <span className="flex flex-col items-center gap-y-3">
                    <DisplayServiceIcon variant={colorVariant} status={status}>{icon}</DisplayServiceIcon>
                    <Link href={href} className="text-xs text-center px-1">{name}</Link>
                </span>
            ) : (
                <Link href={href} className="flex flex-col items-center gap-y-3">
                    <DisplayServiceIcon variant={colorVariant} status={status}>{icon}</DisplayServiceIcon>
                    <span className="text-xs text-center px-1">{name}</span>
                </Link>
            )}
        </li>
    );
};


const page = async () => {
    const data: PageContentResponse = await getPageContent("car-tickets")

    const { schema, description, faqs, title } = data





    const services: ReadonlyArray<ServiceItemProps> = [
        {
            icon: <ServiceIcon imagePath="car-crash" />,
            href: CAR_TICKETS_PATH, colorVariant: "yellow",
            name: "خلافی خودرو",
            status: "ACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="motorcycle" />,
            href: MOTOR_TICKETS_PATH,
            colorVariant: "blue",
            name: "خلافی موتور سیکلت",
            status: "ACTIVE",
            type: "motor"
        },
        {
            icon: <ServiceIcon imagePath="service-camera" />,
            href: CAR_VIOLATION_IMAGE_PATH,
            colorVariant: "purple",
            name: "تصویر تخلفات رانندگی",
            status: "ACTIVE",
            type: "car"
        },
        {
            icon: <ServiceIcon imagePath="car-overrun" />,
            href: CAR_INSURANCE_PATH,
            colorVariant: "green",
            name: "استعلام بیمه شخص ثالث",
            status: "ACTIVE",
            type: "car"
        },



        {
            icon: <ServiceIcon imagePath="certificate-service" />,
            href: DRIVING_LICENSE_STATUS_PATH,
            colorVariant: "emerald",
            name: "وضعیت گواهینامه",
            status: "ACTIVE",
            type: "car"
        },
    ]
    return (
        <>
            <div>
                <Container className='px-4 min-h-[85svh]'>
                    <div className=' mt-10 bg-white max-w-[1280px] flex justify-center items-center h-[550px] p-10 rounded-3xl shadow-md border border-gray-200'>
                        <div className='grid grid-cols-5 gap-5 h-full'>
                            <div className='col-span-2 h-[460px] relative z-0 top-10 -left-5 w-full rounded-3xl'>
                                <div className='border-[#E4E6E9] border bg-gray-100 p-8 rounded-3xl relative z-10'>
                                    <div className='space-y-3 mb-8'>
                                        <h1 className='md:text-xl font-bold'>
                                            خرید بیمه شخص ثالث خودرو
                                        </h1>
                                        <p className='text-sm font-light'>
                                            پلاک خودرو و کد ملی صاحب پلاک مورد نظر را وارد کنید
                                        </p>
                                    </div>
                                    <PlateForm />

                                </div>
                                <div className='border-[#E4E6E9] border bg-gray-100/50 h-[410px] absolute z-0 top-10 -left-5 w-full rounded-3xl'>

                                </div>

                            </div>
                            <div className='col-span-3 relative h-full w-[90%] mx-auto'>
                                <Image src={BimeHero} alt='bime-hero' priority fill />

                            </div>
                        </div>

                    </div>
                </Container>
                <section className='h-52 bg-[#F5F6F8] max-w-7xl mx-auto mb-10'>
                    <div className='text-center mt-10 mb-5'>
                        <h2 className='mb-5 font-light text-2xl'>
                            چرا از خودراکس بخریم؟
                        </h2>
                        <p>
                            تجربه‌ای راحت و امن برای انتخاب بهترین بیمه شخص ثالث
                        </p>
                    </div>
                    <div className='grid grid-cols-4 gap-10'>
                        <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                            <div
                                className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                            >
                                <ApprovalChart size={35} />
                            </div>
                            <h3 className='text-lg'>
                                صدور فوری بیمه نامه
                            </h3>
                            <p className='text-center font-light '>
                                بیمه‌نامه شما در همان روز صادر و قابل استفاده خواهد بود.
                            </p>

                        </div>
                        <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                            <div
                                className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                            >
                                <DocumentListIcon />
                            </div>
                            <h3 className='text-lg'>
                                تنوع کامل شرکت‌های بیمه
                            </h3>
                            <p className='text-center font-light '>
                                امکان انتخاب از میان تمامی شرکت‌های معتبر بیمه‌ای کشور.
                            </p>

                        </div>
                        <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                            <div
                                className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                            >
                                <SupportIcon fill='green' />
                            </div>
                            <h3 className='text-lg'>
                                پشتیبانی ۲۴/۷
                            </h3>
                            <p className='text-center font-light '>
                                هر روز هفته و در هر ساعت، همراه شما هستیم.
                            </p>

                        </div>
                        <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                            <div
                                className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                            >
                                <HelpOperatorIcon />
                            </div>
                            <h3 className='text-lg'>
                                مشاوره تخصصی رایگان
                            </h3>
                            <p className='text-center font-light '>
                                کارشناسان ما بهترین گزینه بیمه را متناسب با شرایط شما پیشنهاد می‌دهند.
                            </p>

                        </div>
                    </div>

                </section>

            </div>
            <section className='bg-white'>
                <div className="mx-auto pt-16 px-3">
                    <Faq faqs={faqs} className="mt-16" />

                </div>
                <div className='max-w-7xl mx-auto mt-10 bg-primary p-5 rounded-3xl text-white flex flex-col justify-center items-center gap-5 py-10'>
                    <Image src={DoubleQoute} alt='double-qoute' />
                    <h2 className='md:text-xl'>
                        الزام قانونی بیمه شخص ثالث
                    </h2>
                    <p>
                        بیمه شخص ثالث برای تمام وسایل نقلیه اجباری است و حتی یک روز نداشتن آن، جریمه مالی به همراه دارد
                    </p>
                </div>
                <div className=' mx-auto mt-10 bg-gradient-to-b from-white to-[#F5F6F8] p-10'>
                    <div className='max-w-7xl mx-auto flex flex-col items-center justify-center gap-10'>
                        <div className='text-center'>
                            <h2 className='md:text-2xl mb-3'>
                                مراحل خرید بیمه شخص ثالث
                            </h2>
                            <p className='font-light'>
                                پاسخ به مهم‌ترین سوالاتی که هنگام خرید بیمه شخص ثالث برای کاربران پیش می‌آید
                            </p>
                        </div>
                        <div className='grid grid-cols-3 gap-10'>
                            <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                                <div
                                    className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                    style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                                >
                                    <span className="w-10 h-10 flex items-center justify-center text-2xl bg-gradient-to-l from-[#20A7F4] to-[#1FA4F3]  text-white rounded-full">
                                        1
                                    </span>

                                </div>
                                <h3 className='text-lg'>
                                    استعلام قیمت بهترین بیمه
                                </h3>
                                <p className='text-center font-light '>
                                    با امکان استعلام و مقایسه قیمت معتبرترین شرکت‌های بیمه، بهترین بیمه را انتخاب کنید.
                                </p>

                            </div>
                            <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                                <div
                                    className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                    style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                                >
                                    <span className="w-10 h-10 flex items-center justify-center text-2xl bg-gradient-to-l from-[#F8BE0C] to-[#FFD246]  text-white rounded-full">
                                        2
                                    </span>

                                </div>
                                <h3 className='text-lg'>
                                    تکمیل و بارگذاری مدارک
                                </h3>
                                <p className='text-center font-light '>
                                    تصاویر مدارک مورد نیاز مانند گواهینامه،کارت ماشین و بیمه‌نامه قبلی خود را بارگذاری کنید.
                                </p>

                            </div>
                            <div className='bg-white p-5 border border-gray-300 rounded-3xl flex flex-col gap-5 items-center'>
                                <div
                                    className="w-20 h-20 bg-cover bg-center flex items-center justify-center"
                                    style={{ backgroundImage: "url('/assets/images/icon-bg.png')" }}
                                >
                                    <span className="w-10 h-10 flex items-center justify-center text-2xl bg-gradient-to-l from-[#5C9183] to-[#94C8BA]  text-white rounded-full">
                                        3
                                    </span>

                                </div>
                                <h3 className='text-lg'>
                                    صدور و دریافت بیمه‌نامه
                                </h3>
                                <p className='text-center font-light '>
                                    پس از بررسی مدارک توسط کارشناسان بیمه‌بازار، بیمه‌نامه صادره را در همان روز تحویل بگیرید.
                                </p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section className='bg-[#EBEBEB] h-80'>
                <div className='max-w-7xl flex justify-center items-center flex-col mx-auto p-10 font-light'>
                    <h2 className='mb-3 text-2xl'>
                        نیاز به مشاوره و راهنمایی دارید؟
                    </h2>
                    <p className='mb-5'>
                        شماره تماس و شماره پلاک خود را وارد نمایید تا کارشناسان آیتول در اسرع وقت با شما تماس حاصل نمایند.
                    </p>
                    <button className='bg-[#363A3E] text-white p-2 px-6 rounded-xl'>
                        درخواست مشاوره
                    </button>
                </div>
            </section>
            <section className=''>
                <div className='relative -top-14 max-w-5xl mx-auto bg-primary p-5 rounded-3xl text-white flex justify-between items-center gap-5'>
                    <div className='flex flex-col gap-3 w-[20%] items-center text-gray-500 bg-white p-5 rounded-xl'>
                        <Umbrella />
                        <span className=''>
                            پوشش ها و تعهدات
                        </span>
                    </div>
                    <div className='flex flex-col gap-3 w-[20%] items-center text-gray-500 bg-white p-5 rounded-xl'>
                        <MoneyBagIcon width={25} height={20} />
                        <span className=''>
                            محساسبه و قیمت
                        </span>
                    </div>
                    <div className='flex flex-col gap-3 w-[20%] items-center text-gray-500 bg-white p-5 rounded-xl'>
                        <WithdrawIcon />
                        <span className=''>
                            خرید اقساطی
                        </span>
                    </div>
                    <div className='flex flex-col gap-3 w-[20%] items-center text-gray-500 bg-white p-5 rounded-xl'>
                        <BadgePercent />
                        <span className=''>
                            انتقال تخفیفات
                        </span>
                    </div>
                    <div className='flex flex-col gap-3 w-[20%] items-center text-gray-500 bg-white p-5 rounded-xl'>
                        <ReceiptText />
                        <span className=''>
                            توضیحات تکمیلی
                        </span>
                    </div>
                </div>
                <div className='max-w-5xl mx-auto p-5 bg-white rounded-2xl border mb-5'>
                    <div className='h-[27rem] p-5 overflow-y-auto space-y-3'>
                        <p className='text-justify font-light leading-8'>
                            یمه شخص ثالث، به دلیل اجباری بودن برای خودروها، بازار خیلی گسترده‌ای را در اختیار خود قرار داده است. این نوع بیمه، مالکان خودرو را در مقابل خسارات مالی و جانی که به اشخاص ثالث وارد می‌شود، حمایت می‌کند. با پوشش 100% در صورت وقوع حادثه، بیمه شخص ثالث به مالکان خودرو اطمینان و آرامش خاطر فراوانی می‌بخشد.

                            (با بیمه شخص ثالث و پوشش‌های فوق العاده آن، با خیال راحت پای خود را بر روی پدال گاز فشار دهید.)

                            بیمه شخص ثالث دو نوع خسارات مالی و جانی یا بدنی شخص ثالث را پوشش می‌دهد. بیمه‌گر متعهد به پرداخت حداکثر پوشش بیمه شخص ثالث اتومبیل برای جبران خسارت های‌جانی و مالی مطابق حداکثر تعهدات توافقی در بیمه‌نامه شخص ثالث است.

                            خسارت مالی: خسارت‌هایی كه به اموال اشخاص ثالث وارد می‌شود مانند تصادف و خسارت به خودروی ثالث. چنین خسارت‌هایی، زیان مادی شخص ثالث دارنده وسیله نقلیه موضوع بیمه را شامل می‌گردد.

                            خسارت بدنی: خسارت‌های جانی به اشخاص ثالث مانند: فوت، نقص عضو و هزینه‌های درمان پزشکی ناشی از حادثه هستند. خسارت بدنی پرداختی یا ارش به نوعی از دیه گفته می‌شود که مقدار آن در شرع مقدس تعیین نشده است. این خسارت‌ها صدماتی مانند: شکستگی، نقص عضو، ازکارافتادگی جزئی یا کلی، دیه فوت و هزینه معالجه اشخاص ثالث در اثر حوادث را در برمی‌گیرد
                        </p>
                        <p className='text-justify font-light leading-8'>
                            یمه شخص ثالث، به دلیل اجباری بودن برای خودروها، بازار خیلی گسترده‌ای را در اختیار خود قرار داده است. این نوع بیمه، مالکان خودرو را در مقابل خسارات مالی و جانی که به اشخاص ثالث وارد می‌شود، حمایت می‌کند. با پوشش 100% در صورت وقوع حادثه، بیمه شخص ثالث به مالکان خودرو اطمینان و آرامش خاطر فراوانی می‌بخشد.

                            (با بیمه شخص ثالث و پوشش‌های فوق العاده آن، با خیال راحت پای خود را بر روی پدال گاز فشار دهید.)

                            بیمه شخص ثالث دو نوع خسارات مالی و جانی یا بدنی شخص ثالث را پوشش می‌دهد. بیمه‌گر متعهد به پرداخت حداکثر پوشش بیمه شخص ثالث اتومبیل برای جبران خسارت های‌جانی و مالی مطابق حداکثر تعهدات توافقی در بیمه‌نامه شخص ثالث است.

                            خسارت مالی: خسارت‌هایی كه به اموال اشخاص ثالث وارد می‌شود مانند تصادف و خسارت به خودروی ثالث. چنین خسارت‌هایی، زیان مادی شخص ثالث دارنده وسیله نقلیه موضوع بیمه را شامل می‌گردد.

                            خسارت بدنی: خسارت‌های جانی به اشخاص ثالث مانند: فوت، نقص عضو و هزینه‌های درمان پزشکی ناشی از حادثه هستند. خسارت بدنی پرداختی یا ارش به نوعی از دیه گفته می‌شود که مقدار آن در شرع مقدس تعیین نشده است. این خسارت‌ها صدماتی مانند: شکستگی، نقص عضو، ازکارافتادگی جزئی یا کلی، دیه فوت و هزینه معالجه اشخاص ثالث در اثر حوادث را در برمی‌گیرد
                        </p>
                        <p className='text-justify font-light leading-8'>
                            یمه شخص ثالث، به دلیل اجباری بودن برای خودروها، بازار خیلی گسترده‌ای را در اختیار خود قرار داده است. این نوع بیمه، مالکان خودرو را در مقابل خسارات مالی و جانی که به اشخاص ثالث وارد می‌شود، حمایت می‌کند. با پوشش 100% در صورت وقوع حادثه، بیمه شخص ثالث به مالکان خودرو اطمینان و آرامش خاطر فراوانی می‌بخشد.

                            (با بیمه شخص ثالث و پوشش‌های فوق العاده آن، با خیال راحت پای خود را بر روی پدال گاز فشار دهید.)

                            بیمه شخص ثالث دو نوع خسارات مالی و جانی یا بدنی شخص ثالث را پوشش می‌دهد. بیمه‌گر متعهد به پرداخت حداکثر پوشش بیمه شخص ثالث اتومبیل برای جبران خسارت های‌جانی و مالی مطابق حداکثر تعهدات توافقی در بیمه‌نامه شخص ثالث است.

                            خسارت مالی: خسارت‌هایی كه به اموال اشخاص ثالث وارد می‌شود مانند تصادف و خسارت به خودروی ثالث. چنین خسارت‌هایی، زیان مادی شخص ثالث دارنده وسیله نقلیه موضوع بیمه را شامل می‌گردد.

                            خسارت بدنی: خسارت‌های جانی به اشخاص ثالث مانند: فوت، نقص عضو و هزینه‌های درمان پزشکی ناشی از حادثه هستند. خسارت بدنی پرداختی یا ارش به نوعی از دیه گفته می‌شود که مقدار آن در شرع مقدس تعیین نشده است. این خسارت‌ها صدماتی مانند: شکستگی، نقص عضو، ازکارافتادگی جزئی یا کلی، دیه فوت و هزینه معالجه اشخاص ثالث در اثر حوادث را در برمی‌گیرد
                        </p>
                        <p className='text-justify font-light leading-8'>
                            یمه شخص ثالث، به دلیل اجباری بودن برای خودروها، بازار خیلی گسترده‌ای را در اختیار خود قرار داده است. این نوع بیمه، مالکان خودرو را در مقابل خسارات مالی و جانی که به اشخاص ثالث وارد می‌شود، حمایت می‌کند. با پوشش 100% در صورت وقوع حادثه، بیمه شخص ثالث به مالکان خودرو اطمینان و آرامش خاطر فراوانی می‌بخشد.

                            (با بیمه شخص ثالث و پوشش‌های فوق العاده آن، با خیال راحت پای خود را بر روی پدال گاز فشار دهید.)

                            بیمه شخص ثالث دو نوع خسارات مالی و جانی یا بدنی شخص ثالث را پوشش می‌دهد. بیمه‌گر متعهد به پرداخت حداکثر پوشش بیمه شخص ثالث اتومبیل برای جبران خسارت های‌جانی و مالی مطابق حداکثر تعهدات توافقی در بیمه‌نامه شخص ثالث است.

                            خسارت مالی: خسارت‌هایی كه به اموال اشخاص ثالث وارد می‌شود مانند تصادف و خسارت به خودروی ثالث. چنین خسارت‌هایی، زیان مادی شخص ثالث دارنده وسیله نقلیه موضوع بیمه را شامل می‌گردد.

                            خسارت بدنی: خسارت‌های جانی به اشخاص ثالث مانند: فوت، نقص عضو و هزینه‌های درمان پزشکی ناشی از حادثه هستند. خسارت بدنی پرداختی یا ارش به نوعی از دیه گفته می‌شود که مقدار آن در شرع مقدس تعیین نشده است. این خسارت‌ها صدماتی مانند: شکستگی، نقص عضو، ازکارافتادگی جزئی یا کلی، دیه فوت و هزینه معالجه اشخاص ثالث در اثر حوادث را در برمی‌گیرد
                        </p>
                    </div>
                </div>
            </section>
            <section className='max-w-7xl mx-auto flex flex-col justify-center items-center gap-10 mb-5 mt-14'>
                <div className='text-center'>
                    <h2 className='md:text-2xl mb-3'>
                        سرویس های مشابه دیگر ما
                    </h2>
                    <p className='font-light'>
                        با سرویس‌های متنوع ما، انتخاب‌های بیشتری در اختیار دارید.
                    </p>
                </div>
                <div className='w-full mx-auto'>
                    <ul className="w-full flex justify-between">
                        {services.map((service, index) => (
                            (
                                <ServiceItem
                                    key={index}
                                    {...service}
                                />
                            )
                        ))}
                    </ul>
                </div>
            </section>
            <section className='bg-primary p-10 mt-10'>
                <div className='max-w-7xl mx-auto '>
                    <div className='text-white text-center mb-10'>
                        <h2 className='md:text-2xl mb-3'>
                            خودروکس از نگاه مشتریان
                        </h2>
                        <p className='font-light'>
                            همیشه برای خواندن نظرات شما آماده‌ایم.
                        </p>
                    </div>
                    <div className='flex justify-between items-center gap-10'>
                        <div className='text-white bg-[#343E50] flex flex-col p-5 rounded-3xl gap-5'>
                            <div className='flex gap-5 items-center'>
                                <div>
                                    <Image src={Customer1} alt='customer-1' />
                                </div>
                                <div>
                                    <h3>
                                        فاطمه سپهری
                                    </h3>
                                    <div className='flex items-center gap-1 text-sm mt-2'>
                                        <Star size={18} fill='#F7BC06' color='#F7BC06' /><Star size={18} fill='#F7BC06' color='#F7BC06' />
                                        <Star size={18} fill='#F7BC06' color='#F7BC06' /><Star size={18} fill='#F7BC06' color='#F7BC06' />
                                        <Star size={18} fill='gray' color='gray' />

                                    </div>
                                </div>
                            </div>
                            <div className='font-light px-1'>
                                <p>
                                    بدون دغدغه زمان و مکان، خرید بیمه را انجام دادم و این خیلی عالیه. قیمت خودروکس از بقیه جاها کمتر بود و علاوه بر اون کد تخفیف هم داده بودن
                                </p>
                            </div>
                        </div>
                        <div className='bg-white text-[#343E50] flex flex-col p-5 rounded-3xl gap-5'>
                            <div className='flex gap-5 items-center'>
                                <div>
                                    <Image src={Customer2} alt='customer-1' />
                                </div>
                                <div>
                                    <h3>
                                        محمد سپهری
                                    </h3>
                                    <div className='flex items-center gap-1 text-sm mt-2'>
                                        <Star size={18} fill='#F7BC06' color='#F7BC06' /><Star size={18} fill='#F7BC06' color='#F7BC06' />
                                        <Star size={18} fill='#F7BC06' color='#F7BC06' /><Star size={18} fill='#F7BC06' color='#F7BC06' />
                                        <Star size={18} fill='gray' color='gray' />

                                    </div>
                                </div>
                            </div>
                            <div className='font-light px-1'>
                                <p>
                                    بدون دغدغه زمان و مکان، خرید بیمه را انجام دادم و این خیلی عالیه. قیمت خودروکس از بقیه جاها کمتر بود و علاوه بر اون کد تخفیف هم داده بودن
                                </p>
                            </div>
                        </div>
                        <div className='text-white bg-[#343E50] flex flex-col p-5 rounded-3xl gap-5'>
                            <div className='flex gap-5 items-center'>
                                <div>
                                    <Image src={Customer1} alt='customer-1' />
                                </div>
                                <div>
                                    <h3>
                                        فاطمه سپهری
                                    </h3>
                                    <div className='flex items-center gap-1 text-sm mt-2'>
                                        <Star size={18} fill='#F7BC06' color='#F7BC06' /><Star size={18} fill='#F7BC06' color='#F7BC06' />
                                        <Star size={18} fill='#F7BC06' color='#F7BC06' /><Star size={18} fill='#F7BC06' color='#F7BC06' />
                                        <Star size={18} fill='gray' color='gray' />

                                    </div>
                                </div>
                            </div>
                            <div className='font-light px-1'>
                                <p>
                                    بدون دغدغه زمان و مکان، خرید بیمه را انجام دادم و این خیلی عالیه. قیمت خودروکس از بقیه جاها کمتر بود و علاوه بر اون کد تخفیف هم داده بودن
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <div className="mx-auto pt-16 px-3">
                <Faq faqs={faqs} className="mt-16" />

            </div>
            <section className='max-w-7xl mx-auto py-12 mb-10'>
                <div className='text-center mb-10'>
                    <h2 className='md:text-2xl mb-3'>
                        بیمه‌های تحت پوشش خودروکس
                    </h2>
                    <p className='font-light'>
                        تمامی شرکت‌های معتبر بیمه‌ای کشور در خودروکس در دسترس شما هستند
                    </p>
                </div>
                <div className='flex justify-between mt-8 gap-3'>
                    <div className='bg-white p-5 rounded-2xl w-[10%] flex justify-center'>
                        <Image src={Iran} alt='iran' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[10%] flex justify-center'>
                        <Image src={Dana} alt='dana' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[10%] flex justify-center'>
                        <Image src={Moalem} alt='moalem' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[10%] flex justify-center'>
                        <Image src={Novin} alt='novin' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[10%] flex justify-center'>
                        <Image src={Pasargad} alt='pasargad' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[10%] flex justify-center'>
                        <Image src={Saman} alt='saman' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[10%] flex justify-center'>
                        <Image src={Sarmad} alt='sarmad' />
                    </div>
                    <div className='bg-white p-5 rounded-2xl w-[10%] flex justify-center'>
                        <Image src={Taavon} alt='taavon' />
                    </div>
                </div>
            </section>
            <section className='bg-[#F5F6F8] py-5 md:py-10'>
                <HomeBlogSection />
            </section>

        </>
    )
}

export default page