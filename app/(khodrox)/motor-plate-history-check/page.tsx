import BlogComments from '@/components/blog/SinglePage/BlogComments'
import ChildSchema from '@/components/common/ChildSchema'
import InquiryComponent from '@/components/inquiry/InquiryComponent'
import ArticleSection from '@/components/InquiryStaticComponents/ArticleSection'
import Faq from '@/components/InquiryStaticComponents/Faq'
import envConfig from '@/lib/config-env'
import { PageContentResponse } from '@/lib/types/types'
import { getPageContent } from '@/lib/utils'

export async function generateMetadata() {
    const data = await getPageContent("motor-plate-history-check");

    return {
        title: data.meta_title,
        description: data.meta_description,
         keywords: data.tags || [],
    ...(data.meta_search && { robots: data.meta_search }),
    ...(data.canonical && { alternates: { canonical: data.canonical } }),
    };
}

const isMotor: boolean = true
const withDetails: boolean | undefined = false
const MotorPlateHistoryPage = async () => {
//    const env = envConfig()
    const status = process.env.NEXT_PUBLIC_MOTOR_PLATE_HISTORY_CHECK!
    const data: PageContentResponse = await getPageContent("motor-plate-history-check")
    

    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id="motor-plate-history"
                    schema={schema}
                />
            }
            <InquiryComponent
                title={title || ""}
                isMotor={isMotor}
                withDetails={withDetails}
                status={status}
                boxTitle="استعلام تاریخچه پلاک موتور سیکلت"
            />
            {/* <PlateHistoryAbout/> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10">
                </Faq>
            }
            <div className="md:max-w-7xl mx-auto mb-10">
                  {/* <UserComments /> */}
                  <BlogComments contentId={data.id} />
            </div>
        </>
    );
}

export default MotorPlateHistoryPage