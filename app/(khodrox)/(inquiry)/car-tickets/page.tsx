import InquiryComponent from "@/components/inquiry/InquiryComponent";
import Faq from "@/components/InquiryStaticComponents/Faq";
import "@/styles/styles.css"
import envConfig from "@/lib/config-env";
import MotherSchema from "@/components/common/MotherSchema";
import { PageContentResponse } from "@/lib/types/types";
import ArticleSection from "@/components/InquiryStaticComponents/ArticleSection";
import { getPageContent } from "@/lib/utils";
import BlogComments from "@/components/blog/SinglePage/BlogComments";

export async function generateMetadata() {
    const data = await getPageContent("car-tickets");

    return {
        title: data.meta_title,
        description: data.meta_description,
        keywords: data.tags || [],
    ...(data.meta_search && { robots: data.meta_search }),
    ...(data.canonical && { alternates: { canonical: data.canonical } }),
    };
}




export const revalidate = 0

const isMotor: boolean = false
const withDetails: boolean | undefined = undefined
export default async function CarTicketsPage() {
    const env = envConfig()
    const status = env.Services.VEHICLE_VIOLATION_SECTION
    
    const data: PageContentResponse = await getPageContent("car-tickets")

    const { schema, description, faqs, title } = data


    return (
        <>
            {
                schema &&
                <MotherSchema
                    id="car-tickets"
                    schema={schema}
                />
            }
            {/* <div className="md:max-w-[950px] mx-auto">

            </div> */}
            <InquiryComponent
                title={title || ""}
                isMotor={isMotor}
                withDetails={withDetails}
                status={status} />
            {
                description &&
                <ArticleSection description={description} />
            }
            {/* <OtherServices/> */}
            {
                faqs &&
                <div className="mx-auto mt-10 px-3">
                    <Faq faqs={faqs} className="mt-10 mb-10" />

                </div>
            }
            <div className="md:max-w-7xl mx-auto mb-10">
                  {/* <UserComments /> */}
                  <BlogComments contentId={data.id} />
            </div>
        </>
    );
}
