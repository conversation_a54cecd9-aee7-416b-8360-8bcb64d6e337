import InquiryComponent from "@/components/inquiry/InquiryComponent";
import Faq from "@/components/InquiryStaticComponents/Faq";
import { getPageContent } from "@/lib/utils";
import envConfig from "@/lib/config-env";
import ChildSchema from "@/components/common/ChildSchema";
import { PageContentResponse } from "@/lib/types/types";
import ArticleSection from "@/components/InquiryStaticComponents/ArticleSection";
import CarViolationImageStepModal from "@/components/inquiry/CarViolationImageStepModal";
import BlogComments from "@/components/blog/SinglePage/BlogComments";

export async function generateMetadata() {
    const data = await getPageContent("car-tickets/car-violation-image");

    return {
        title: data.meta_title,
        description: data.meta_description,
        keywords: data.tags || [],
    ...(data.meta_search && { robots: data.meta_search }),
    ...(data.canonical && { alternates: { canonical: data.canonical } }),
    };
}

export const revalidate = 0

const isMotor: boolean = false
const withDetails: boolean | undefined = true



export default async function CarViolationImagePage() {

    const env = envConfig()
    const status = env.Services.DRIVING_VIOLATION_IMAGE_SECTION

    const data: PageContentResponse = await getPageContent("car-tickets/car-violation-image")

    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id="car-violation-image"
                    schema={schema}
                />
            }
            <InquiryComponent
                title={title || ""}
                isMotor={isMotor}
                withDetails={withDetails}
                status={status}
            />
            <CarViolationImageStepModal />
            {
                description &&
                <ArticleSection description={description} />
            }
            {/* <OtherServices/> */}
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
            <div className="md:max-w-7xl mx-auto mb-10">
                  {/* <UserComments /> */}
                  <BlogComments contentId={data.id} />
            </div>
        </>
    );
}
