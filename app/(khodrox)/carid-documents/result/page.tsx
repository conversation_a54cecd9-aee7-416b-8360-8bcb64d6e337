import CarDocumentsResult from '@/components/CarIdDocuments/CarDocumentsResult'
import Card from '@/components/common/Card'
import Container from '@/components/common/Container'
import LicensePointResult from '@/components/DrivingLicensePoint/LicensePointResult'
import Link from 'next/link'
import React from 'react'

const CarIdDocumentsResultPage = () => {
  return (
      <Container>
            <Card className="!px-5 !pt-5 !pb-10 mt-5  min-h-96 w-full max-w-lg mb-20 md:mb-32 relative z-20">
                <h2 className="text-[#212121] text-base md:text-lg font-semibold mb-4 py-3">
                    نتیجه استعلام کارت و سند خودرو
                </h2>

                <CarDocumentsResult />
               


                <Link href={"/carid-documents"} className=" w-full mt-12 rounded-lg text-white mx-auto bg-primary hover:bg-blue-500 transition-all p-4 px-8 flex items-center justify-center">
                    <span className="text-base">استعلام جدید</span>
                </Link>
            </Card>

        </Container>
  )
}

export default CarIdDocumentsResultPage