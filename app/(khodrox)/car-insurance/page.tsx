import Faq from '@/components/InquiryStaticComponents/Faq'
import { getPageContent } from '@/lib/utils'
import envConfig from "@/lib/config-env";
import InsuranceComponent from "@/components/CarInsurance/InsuranceComponent";
import ChildSchema from '@/components/common/ChildSchema';
import { PageContentResponse } from '@/lib/types/types';
import ArticleSection from '@/components/InquiryStaticComponents/ArticleSection';
import BlogComments from '@/components/blog/SinglePage/BlogComments';

export async function generateMetadata() {
    const data = await getPageContent("car-insurance");

    return {
        title: data.meta_title,
        description: data.meta_description,
        keywords: data.tags || [],
    ...(data.meta_search && { robots: data.meta_search }),
    ...(data.canonical && { alternates: { canonical: data.canonical } }),
    };
}

export const revalidate = 0

const isMotor: boolean = false
const CarInsurancePage = async () => {
    const env = envConfig()
    const status = env.Services.THIRD_PARTY_INSURANCE_INQUIRY_SECTION
    const data: PageContentResponse = await getPageContent("car-insurance")
    const { schema, description, faqs, title } = data
    return (
        <>
            {
                schema &&
                <ChildSchema
                    id='car-insurance'
                    schema={schema}
                />
            }
            <InsuranceComponent
                title={title || ""}
                isMotor={isMotor}
                status={status}
            />
            {/* <CarInsuranceAboutService /> */}
            {
                description &&
                <ArticleSection description={description} />
            }
            {
                faqs &&
                <Faq faqs={faqs} className="mt-10 mb-10" />
            }
            <div className="md:max-w-7xl mx-auto mb-10">
                  {/* <UserComments /> */}
                  <BlogComments contentId={data.id} />
            </div>
        </>
    )
}

export default CarInsurancePage