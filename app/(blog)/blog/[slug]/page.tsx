

import dynamic from 'next/dynamic';
import Ads from "@/public/assets/images/ads3.png";
import Image from "next/image";
import ArticleSlider from "@/components/blog/SinglePage/ArticleSlider";
import BlogHead from "@/components/blog/SinglePage/BlogHead";
import BlogPostCategory from "@/components/blog/SinglePage/BlogPostCategory";

import { AboutAuthor } from "@/components/blog/SinglePage/AboutAuthor";
import MobileShareSection from "@/components/blog/SinglePage/MobileShareSection";

import RecommendedPost from "@/components/blog/SinglePage/RecommendedPost";
import BlogSearchBox from "@/components/blog/SinglePage/BlogSearchBox";
import BlogCategoriesList from "@/components/blog/SinglePage/BlogCategoriesList";
import NewestArticlesList from "@/components/blog/SinglePage/NewestArticlesList";
import { apiClient } from "@/lib/apiClient";
import { ArticleResponse, CateoryResponse } from "@/lib/types/article.types";
import { cache } from 'react';

import type { Metadata } from 'next'
import ChildSchema from "@/components/common/ChildSchema";
import BlogMainContent from "@/components/blog/main/BlogMainContent";
import Faq from "@/components/InquiryStaticComponents/Faq";
import Link from "next/link";


import { ArticlesApiResponse } from '@/lib/types/types';

const BlogComments = dynamic(() => import('@/components/blog/SinglePage/BlogComments'))



const getArticle = cache(async (slug: string): Promise<ArticleResponse> => {
  const response = await apiClient(`article/view/${slug}`);
  console.log("--------------------", " :  api called for slug:", slug);
  console.log(response);
  return (await response.json()) as ArticleResponse;
});




function isNumeric(value: string) {
  return /^\d+$/.test(value);
}

export async function generateMetadata(
  { params }: { params: Promise<{ slug: string }> }
): Promise<Metadata> {
  const { slug } = await params;


  if (isNumeric(slug)) {
    const page = parseInt(slug, 10);
    const baseUrl = "https://khodrox.com";
    const ArticlesResponse: ArticlesApiResponse = await apiClient(`articles`)
      .then(res => res.json())

    const prev =
      page > 1 ? `${baseUrl}/${page - 1}` : undefined;
    const next =
      page < ArticlesResponse.meta.last_page ? `${baseUrl}/${page + 1}` : undefined; // اینجا ۵ رو می‌تونی داینامیک کنی

    return {
      title: `صفحه ${page} دسته‌بندی`,
      alternates: {
        canonical: `${baseUrl}/page=${page}`,
      },
      other: {
        ...(prev && { "link:prev": prev }),
        ...(next && { "link:next": next }),
      },
    };
  }


  const articleData = await getArticle(slug);
  const article = articleData.data;


  return {
    title: article.meta_title || article.title,
    description: article.meta_description || "",
    keywords: article.tags || [],
    ...(article.meta_search && { robots: article.meta_search }),
    ...(article.canonical && { alternates: { canonical: article.canonical } }),
  };
}



/**
 * Blog post page component
 * Renders individual blog articles with sidebar, comments, and related content
 * Uses cached getArticle function to avoid duplicate API calls with generateMetadata
 * @param params - Route parameters containing the article slug
 * @returns JSX.Element - The rendered blog post page
 */
const BlogPostPage = async ({
  params,
}: {
  params: Promise<{ slug: string }>;
}) => {
  const { slug: rawSlug } = await params;
  const slug = decodeURIComponent(rawSlug);

  // Decode the URL-encoded Persian characters
  const decodedSlug = decodeURIComponent(slug)

  // Log the decoded slug to verify it's correct
  console.log("Decoded slug for API call:", decodedSlug)

  // Use the decoded slug in the API call
  const articleData = await getArticle(decodedSlug);
  const article = articleData.data;

  // Note: Redirect logic is now handled by route.ts for proper HTTP redirects
  // This ensures curl and other HTTP clients receive proper redirect status codes

  const CategoriesResponse: CateoryResponse = await apiClient("categories")
    .then(res => res.json())
  console.log(CategoriesResponse);

  // Access article data using the typed interface
  // const article = articleData.data
  // Check if article.ads exists and if any of its properties are truthy
  const articleAds = article.ads && Boolean(article.ads.cover || article.ads.description || article.ads.title || article.ads.link)

  console.log(article)
  const schema = article.schema




  return (
    <>


      {
        article.type == "article" ?
          <>
            {
              schema &&
              <ChildSchema
                id={decodedSlug}
                schema={schema}
              />
            }
            <div className=" bg-gradient-to-b from-white to-[#F5F6F8] ">
              <div className="min-md:h-[85vh] relative ">
                <div className="md:bg-gray-hero h-96 max-md:h-52 top-0 absolute left-0 w-full"></div>
                <div className="container mx-auto flex justify-between">
                  <div className={article.sidebar_right ? "md:w-[73%]" : "md:max-w-[950px] w-full mx-auto"}>
                    <BlogHead {...articleData.data} />
                    <div className=" max-md:hidden mb-10 pb-10  mx-auto mt-8 max-md:mt-5">
                      <BlogPostCategory category={article.category} />

                    </div>



                    <section className='SeoSection article-section max-md:mt-5 about-service p-3.5 mx-auto md:max-w-7xl pt-5' dangerouslySetInnerHTML={{ __html: article.description }}>

                    </section>
                    <div className=" mx-auto mt-10 px-3">
                      <Faq faqs={article.faqs} className="mt-10 mb-10" />
                      <AboutAuthor authorAvatar={article.author_avatar || ""} authorAbout={article.author_about} authorFullName={article.author_fullname} />
                      <section className="weblog-category md:hidden mt-5 bg-yellow max-md:w-[96%] font-bold md:container flex items-center justify-between mx-auto text-black px-5 py-3 rounded-3xl">
                        <MobileShareSection />

                      </section>
                      <ArticleSlider articles={article.random_articles} />

                    </div>
                    {/* <CommentTextArea contentId={article.id} commentType="blog" /> */}
                    {/* {
                  article.comments.length &&
                  <div className=" mx-auto mb-10">
.                    
                  </div>
                } */}
                    <div className=" mx-auto mb-10 px-3">
                      {/* <UserComments /> */}
                      <BlogComments contentId={article.id} />
                    </div>
                  </div>

                  {
                    article.sidebar_right &&
                    <div className="max-md:hidden bg-white p-3 w-[25%] h-fit mt-16 z-[2] rounded-3xl shadow-md">
                      <BlogSearchBox />


                      <RecommendedPost
                        image={article.article_default.cover}
                        url={article.article_default.slug || "#"}
                        title={article.article_default.title}
                      />

                      <BlogCategoriesList categories={CategoriesResponse} />
                      {
                        articleAds &&
                        <Link href={article.ads.link ? article.ads.link : "#"}>
                          <div className="relative mx-auto w-full max-md:w-[100%] mt-4 min-h-[200px] max-h-[500px] rounded-xl overflow-hidden">
                            <Image
                              src={article.ads?.cover || Ads}
                              alt={article.ads.title || "ads"}
                              fill
                              className="rounded-xl"
                              priority
                            />
                          </div>

                        </Link>
                      }

                      <NewestArticlesList />
                      {/* TODO: this part is better to use Image not html */}
                      {/* <div className=" mt-10 flex flex-col gap-3">
                    <SocialCard
                      platformName="Instagram"
                      username="Khodroxcom@"
                      gradientFrom="#FFBF4B"
                      gradientTo="#F6406D"
                      icon={<InstagramIcon className="text-blue-500" size={40} />}
                    />

                    <SocialCard
                      platformName="Telegram"
                      username="Khodroxcom@"
                      gradientFrom="#36AEE2"
                      gradientTo="#1D93D1"
                      icon={<TelegramIcon className="text-blue-500" width={40} />}
                    />


                  </div> */}

                    </div>
                  }
                </div>

              </div>


            </div>

          </>
          :
          <BlogMainContent articleDescription={article.description} articleTitle={article.title_article} ArticlesResponse={article.articles || []} CategoriesResponse={CategoriesResponse} />
      }

    </>
  )
}

export default BlogPostPage