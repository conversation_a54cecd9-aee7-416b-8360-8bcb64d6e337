// import { apiClient } from "@/lib/apiClient"
// import { ArticlesApiResponse } from "@/lib/types/types"
import { Metadata } from "next"
// import { CateoryResponse } from "@/lib/types/article.types"
import BlogMainContent from "@/components/blog/main/BlogMainContent"

export const metadata: Metadata = {
  // robots: 'noindex, nofollow',
};


const BlogPage = async () => {
  
    
  return (
    <BlogMainContent  />
  )

}

export default BlogPage