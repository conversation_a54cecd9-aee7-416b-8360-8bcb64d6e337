
import { getTicketDepartment } from "@/actions/tickets.action"
import ChatPlusIcon from "@/components/common/svg/ChatPlusIcon"
import CreateTicketClient from "@/components/Dashboard/tickets/CreateTicketClient";
import { ApiResponse } from "@/lib/types/favorites.types";
import Link from "next/link";

export interface DepartmentData {
    id: number;
    title: string;
}

const CreateTicketPage = async () => {

    const departments:ApiResponse<DepartmentData[]> = await getTicketDepartment()
    const ticketSericeStatus = process.env.NEXT_PUBLIC_TICKET_SERVICE_STATUS || "DEACTIVE";
    console.log(departments);
    return (
        <div className="bg-white p-5 rounded-xl min-h-[20rem] shadow-md">
            <div className="flex items-center gap-3 h-[3rem]">
                <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent h-full p-2 pt-2.5 rounded-b-full">
                    <ChatPlusIcon className="" />
                </div>
                <h1>
                    ارسال تیکت جدید
                </h1>
            </div>
            <div className="p-1 mt-5">
                {
                    ticketSericeStatus === "DEACTIVE" ? (
                        <>
                            <p className="bg-[#FFF5D8] p-2 border-2 border-[#F7BC06] border-dashed max-md:text-sm leading-8 rounded-xl">
                                در حال حاضر این بخش غیرفعال است. لطفا از طریق بخش <Link href={"/contact-us"} className="text-primary underline">تماس با ما</Link> با ما در ارتباط باشید.
                            </p>
                        </>
                    ) : null
                }
               

                <CreateTicketClient departments={departments?.data || []} ticketServiceStatus={ticketSericeStatus}  />
            </div>
        </div>
    )
}

export default CreateTicketPage