export const dynamic = "force-dynamic";

// import LastOrders from '@/components/Dashboard/LastOrders'
import PopularServices from '@/components/Dashboard/PopularServices'
import UserLastInquiries from '@/components/Dashboard/UserLastInquiries'
import UserQuickAccess from '@/components/Dashboard/UserQuickAccess'
import WalletAmount from '@/components/Dashboard/WalletAmount'

import React from 'react'
import "@/styles/styles.css"

import { getInquiryHistory } from '@/actions/inquiry.action'
import { getAllInvoices } from '@/actions/invoices.action'
import { AllInvoicesResponse } from '@/lib/types/invoice.types'
import LastOrders from '@/components/Dashboard/LastOrders'
import FavoritesList from '@/components/Dashboard/FavoritesList';
import { getFavorites } from '@/actions/favorites.action';
import { FavoritesResponseData } from '@/lib/types/favorites.types';
import { getUserAddressesAction } from '@/actions/userAddress.action';




const page = async () => {

    const response = await getInquiryHistory()

    const inquiries = response?.data ?? []
    console.log(typeof inquiries.length);
    const invoicesResponse : AllInvoicesResponse = await getAllInvoices()
    const invoices = invoicesResponse?.data ?? []
    console.log(invoices);
    const favoritesResponse = await getFavorites(1,4)
    const favorites: FavoritesResponseData = favoritesResponse?.data ?? []
    console.log(favorites);
    const addressesResponse = await getUserAddressesAction()
    const addresses = addressesResponse?.data ?? []
    

    
    return (
                <div className=' flex flex-col gap-4 h-full'>
                    <UserQuickAccess 
                        inquirieslength={inquiries.length}
                        orderslength={invoices?.invoices?.length || 0}
                        favoritesslength={favorites?.products?.length}
                        addresseslength={addresses.length}
                         />
                    <div className=' grid lg:grid-cols-4 md:grid-cols-1 max-md:grid-cols-1 md:gap-y-4 lg:gap-4 max-md:gap-4'>
                        <UserLastInquiries title='آخرین استعلامات من' inquiries={inquiries} error={response.message} />
                        <div className='w-full flex flex-col gap-4 max-md:px-3 h-full '>
                            <WalletAmount />
                            <PopularServices />
                        </div>
                    </div>
                        {/* <LastOrders invoices={invoices.invoices} /> */}
                        {/* <FavoritesList favorites={favorites.products} /> */}
                </div>
        
    )
}

export default page