import ChatSmileIcon from "@/components/common/svg/ChatSmileIcon"
import FilterIcon from "@/components/common/svg/FilterIcon"
import { ChevronsLeft, Plus, Search } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import ChatSmile from "@/public/assets/images/chat-smile.png"
import ChatIcon from "@/components/common/svg/ChatIcon"
import ChatIconFilled from "@/components/common/svg/ChatIconFilled"
import MoreVerticalDots from "@/components/common/svg/MoreVerticalDots"
import { getUserTcikets } from "@/actions/tickets.action"
import { ApiResponse } from "@/lib/types/favorites.types"
import { TicketsResponse } from "@/lib/types/tickets.types"
import TicketsClient from "@/components/Dashboard/tickets/TicketsClient"



const TicketsPage = async () => {
    const response: ApiResponse<TicketsResponse> = await getUserTcikets()
    const tickets = response?.data?.tickets ?? []
    console.log(tickets);
    const ticketSericeStatus = process.env.NEXT_PUBLIC_TICKET_SERVICE_STATUS || "DEACTIVE";


    return (
        <section>
            
            {/* <div className="mt-8 flex justify-between">
                <div className="bg-white rounded-2xl p-4 w-[40%] h-[35rem]">
                    <div className="flex justify-between px-5">
                        <div className="flex items-center gap-2">
                            <ChatSmileIcon />
                            <p>تیکت ها</p>
                            <span> ({tickets.length}) </span>
                        </div>
                        <div>
                            <Image src={ChatSmile} alt="chat-smile" />
                        </div>
                    </div>
                   
                </div>
            </div> */}
            {
                ticketSericeStatus === "DISABLED" ? (
                    <>
                        <p className="bg-[#FFF5D8] p-2 border-2 border-[#F7BC06] border-dashed max-md:text-sm leading-8 rounded-xl">
                            در حال حاضر این بخش غیرفعال است. لطفا از طریق بخش <Link href={"/contact-us"} className="text-primary underline">تماس با ما</Link> با ما در ارتباط باشید.
                        </p>
                    </>
                ) : null
            }           
                <TicketsClient tickets={tickets} ticketServiceStatus={ticketSericeStatus} />
        </section>
    )
}

export default TicketsPage