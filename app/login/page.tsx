// app/login/page.tsx
"use client"

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { SetNaghlieCookie } from '@/actions/other.action'
import { useAuth } from '@/lib/hooks/useAuth'
// import LoginBox from '@/components/Login/LoginBox'
import dynamic from 'next/dynamic'
const LoginBox = dynamic(() => import('@/components/Login/LoginBox'), {
    loading: () => <div className=''></div>,
    ssr: false,
  });
import { useRouter } from 'next/navigation'
import { logginUserFromKhalafiyar } from '@/actions/auth.action'
// import { postInquireVehicle } from '@/actions/inquiry.action'
import toast from 'react-hot-toast'
export interface AuthParams {
  withDetails: boolean | null;
  plateLeft: string | null;
  plateMiddle: string | null;
  plateRight: string | null;
  plateAlphabet: string | null;
  phoneNumber: string | null;
  nationalCode: string | null;
//   userId: string | null;
  phone: string | null;
  token: string | null;
  type: string | null;
}

export default function LoginPage() {
    const searchParams = useSearchParams()
    const { reFetchUser } = useAuth()
    const [isLoading, setIsLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    // const APPLICATION_TOKEN = process.env.NEXT_PUBLIC_X_APPLICATION_TOKEN as string;
    //   const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL as string;
    const router = useRouter()
// debugger
    useEffect(() => {
        async function handleAuth() {
            if (!searchParams.get('token')) {
               
                setIsLoading(false)
                return
            }
            try {
                
                
                const params: AuthParams = {
                    withDetails: searchParams.get('withDetails') === 'true',                    plateLeft: searchParams.get('plateLeft'),
                    plateMiddle: searchParams.get('plateMiddle'),
                    plateRight: searchParams.get('plateRight'),
                    plateAlphabet: searchParams.get('plateAlphabet'),
                    phoneNumber: searchParams.get('phoneNumber'),
                    nationalCode: searchParams.get('nationalCode'),
                    // userId: searchParams.get('userId'),
                    phone: searchParams.get('phone'),
                    token: searchParams.get('token'),
                    type: searchParams.get('type')
                }

                // const apiUrl = `${BASE_URL}auth/deposit/transfer`                
                // const response = await fetch(apiUrl, {
                //     method: "POST",
                //     headers: {
                //         "Content-Type": "application/json",
                //         "Accept": "application/json",
                //         "X-Application-Token": APPLICATION_TOKEN,
                //     },
                //     body: JSON.stringify({
                //         userId: params.userId || null,
                //         phone: params.phone || null,
                //         type: params.type || null,
                //         withDetails: params.withDetails || null,
                //         plaque: {
                //             plateLeft: params.plateLeft || null,
                //             plateMiddle: params.plateMiddle || null,
                //             plateRight: params.plateRight || null,
                //             plateAlphabet: params.plateAlphabet || null
                //         },
                //         phoneNumber: params.phoneNumber || null,
                //         nationalCode: params.nationalCode || null,
                //         amount: params.amount || null,
                //         callback_url: `${window.location.origin}/wallet/result`
                //     })
                // })
                //debugger
                //const callback_url = `${window.location.origin}/wallet/result`
                
               const response = await logginUserFromKhalafiyar(params)

                // Check if response is JSON
               
                // router.push(data.data.payment_link)
                // if (!response.ok) {
                //     throw new Error(data.message || 'API request failed')
                // }
                
               if (response?.data?.access_token) {
                    await SetNaghlieCookie(`Bearer ${response.data.access_token}`)

                    const { phoneNumber, nationalCode, type } = response.data.details
                    const { withDetails } = response.data
                    const { plateLeft, plateMiddle, plateRight, plateAlphabet } = response.data.details.plaque

                    const query = new URLSearchParams({
                        type,
                        phoneNumber,
                        nationalCode,
                        withDetails,
                        plateLeft,
                        plateMiddle,
                        plateRight,
                        plateAlphabet
                    }).toString()

                    router.push(`result?${query}`)
                }else{
                    toast.error(response?.data?.message || "خطایی رخ داده. لطفا مجددا تلاش کنید",{duration: 3000})
                    setTimeout(() => {                        
                        window.location.href = '/login';
                    }, 3000);
                    // return window.location.href = '/login';
                }


            } catch (error) {
                setIsLoading(false)
                console.error('Authentication error:', error)
                setError(error instanceof Error ? error.message : 'Unknown error occurred')
            } finally {
                // setIsLoading(false)
                // router.push("/")
                await reFetchUser()
            }
        }

        handleAuth()
    }, [])

    if (isLoading) {
        return (
            <div className="flex flex-col gap-5 justify-center items-center h-[90vh]">
                <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
                <div className='flex flex-col items-center gap-3'>
                    {/* <p>پرداخت موفق</p> */}
                    <p> در حال دریافت اطلاعات ... </p>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div className="flex justify-center items-center h-[90vh]">
                <div className="text-red-500 p-4 border border-red-400 rounded">
                    Error: {error}
                </div>
            </div>
        )
    }

    return <LoginBox />
}