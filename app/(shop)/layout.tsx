import ShopNavbar from "@/components/Header/ShopNavbar";
import Footer from "@/components/UI/Footer";
import type {ReactNode} from "react";
import "@/styles/styles.css"
import {Metadata} from "next";
import {CartProvider} from "@/lib/context/cart-context";
import ShopMobileFooter from "@/components/shop/ShopMobileFooter";
import ShopHeaderWrapper from "@/components/Header/ShopHeaderWrapper";

type Props = {
    children: ReactNode;
}
export const metadata: Metadata = {
    robots: 'noindex, nofollow',
};
export default function layout({children}: Props) {
    return (
        <>
            <ShopNavbar/>
            <CartProvider>
                <ShopHeaderWrapper />
                {children}
                <ShopMobileFooter/>
            </CartProvider>
            <Footer/>

        </>
    );
}
