import { NextRequest, NextResponse } from 'next/server'
import { AUTH<PERSON><PERSON>ZATION, RETURN_URL } from "@/lib/constants"
import { DASHBOARD_PATH, HOME_PATH, LOGIN_PATH, protectedRoutes, signOutOnlyRoutes } from "@/lib/routes"
import authService from "@/lib/services/auth.service"
import { NextURL } from "next/dist/server/web/next-url"
import cookieService from "@/lib/services/cookie-service"
import { apiClient } from "@/lib/apiClient"

// List of parameters that indicate a special cross-website login
// const SPECIAL_LOGIN_PARAMS = [
//   'type',
//   'withDetails',
//   'plateLeft',
//   'plateMiddle',
//   'plateRight',
//   'plateAlphabet',
//   'phoneNumber',
//   'nationalCode',
//   'amount',
//   'userId',
//   'phone'
// ]

export default async function middleware(req: NextRequest) {
  const jwt_token = req.cookies.get(AUTHORIZATION)
  const url = req.nextUrl
  const path = url.pathname


  // Skip middleware for certain paths
  if (path === "/tickets-result") {
    return NextResponse.next()
  }

  // Handle blog article redirects - only for specific article slugs, not the main blog page
  if (path.startsWith('/blog/') && path !== '/blog' && path !== '/blog/') {
    try {
      const slug = path.replace('/blog/', '');

      // Skip if slug is empty or contains only whitespace
      if (!slug || slug.trim() === '') {
        return NextResponse.next();
      }

      const decodedSlug = decodeURIComponent(slug);

      // Make API call to check for redirect
      const response = await apiClient(`article/view/${decodedSlug}`);

      if (response.ok) {
        const articleData = await response.json();
        const article = articleData.data;

        // Check if article has redirect_to field
        if (article.redirect_to) {
          // Handle both relative and absolute URLs
          const redirectUrl = article.redirect_to.startsWith('http')
            ? article.redirect_to
            : new URL(article.redirect_to, url.origin).toString();
          return NextResponse.redirect(redirectUrl, 301);
        }
      }
    } catch (error) {
      console.error('Error checking blog redirect:', error);
      // Continue with normal flow if there's an error
    }
  }

  // Check if this is a special login case
  // const hasSpecialLoginParams = SPECIAL_LOGIN_PARAMS.some(param => 
  //   url.searchParams.has(param)
  // )

const isProtectedRoute =
  path !== LOGIN_PATH && (protectedRoutes.includes(path) || path.startsWith("/dashboard"));  console.log("is protedcted :",isProtectedRoute);
  
  const isShownOnlyWhenSignOut = signOutOnlyRoutes.includes(path)
  const isAuthorized = !!jwt_token?.value && authService.jwtTokenIsValid(jwt_token.value)

  
  if (jwt_token?.value && !isAuthorized) {
    await cookieService.deleteAuthorizationToken()
  }

  // Special case: If user is coming from another website with login params
  // AND is on the login page, skip all redirects
  // console.log(path);
  // console.log(isAuthorized);
  // if (path === LOGIN_PATH && isAuthorized) {
  //   return NextResponse.redirect("/")
  // }
 
  if (isShownOnlyWhenSignOut && isAuthorized) {
    return redirectToHome(url)
  }

  if (path === LOGIN_PATH) {
    return NextResponse.next()
  }
  

  
  if (!isAuthorized && path === DASHBOARD_PATH) {
    const loginUrl = new URL(LOGIN_PATH, url.origin)
    return NextResponse.redirect(loginUrl)
  }
  
  
  if (isProtectedRoute && !isAuthorized) {
    return redirectToLogin(url)
  }
  
  
  return NextResponse.next()
}

function redirectToHome(url: NextURL) {
  const homeUrl = new URL(HOME_PATH, url.origin)
  return NextResponse.redirect(homeUrl)
}

function redirectToLogin(url: NextURL) {
  const loginUrl = new URL(LOGIN_PATH, url.origin)
  loginUrl.searchParams.set(RETURN_URL, url.pathname + url.search)
  return NextResponse.redirect(loginUrl)
}

export const config = {
  matcher: [
    "/((?!api|_next|.*\\.(?:png|jpg|jpeg|svg|webp|ico|js|json)|firebase-messaging-sw\\.js|favicon\\.ico).*)",
  ],
};
