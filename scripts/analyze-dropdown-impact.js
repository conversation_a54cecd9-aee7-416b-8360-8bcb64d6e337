#!/usr/bin/env node

/**
 * <PERSON>ript to analyze the bundle size impact of dropdown menu optimization
 * This script helps measure the before/after impact of switching to lite version
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

async function analyzeBundleSize(label) {
  console.log(`\n📊 Analyzing bundle size (${label})...`);
  
  try {
    // Run build with bundle analyzer
    const output = execSync('npm run analyze', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    // Extract relevant metrics from build output
    const lines = output.split('\n');
    const routeLines = lines.filter(line => line.includes('kB') && line.includes('/'));
    
    let totalFirstLoadJS = 0;
    let routeCount = 0;
    
    routeLines.forEach(line => {
      const match = line.match(/(\d+\.?\d*)\s*kB/g);
      if (match && match.length >= 2) {
        const firstLoadJS = parseFloat(match[1].replace(' kB', ''));
        if (!isNaN(firstLoadJS)) {
          totalFirstLoadJS += firstLoadJS;
          routeCount++;
        }
      }
    });
    
    const avgFirstLoadJS = routeCount > 0 ? (totalFirstLoadJS / routeCount).toFixed(2) : 0;
    
    console.log(`📈 Routes analyzed: ${routeCount}`);
    console.log(`📦 Average First Load JS: ${avgFirstLoadJS} kB`);
    
    return {
      routeCount,
      avgFirstLoadJS: parseFloat(avgFirstLoadJS),
      totalFirstLoadJS
    };
    
  } catch (error) {
    console.error('❌ Error running bundle analysis:', error.message);
    return null;
  }
}

async function compareVersions() {
  console.log('🔍 Starting dropdown menu bundle size comparison...');
  
  // Analyze original version
  console.log('\n1️⃣ Analyzing ORIGINAL dropdown menu...');
  const originalStats = await analyzeBundleSize('Original');
  
  if (!originalStats) {
    console.log('❌ Failed to analyze original version');
    return;
  }
  
  // Migrate to lite version
  console.log('\n2️⃣ Migrating to LITE version...');
  try {
    execSync('node scripts/migrate-dropdown.js to-lite', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    return;
  }
  
  // Analyze lite version
  console.log('\n3️⃣ Analyzing LITE dropdown menu...');
  const liteStats = await analyzeBundleSize('Lite');
  
  if (!liteStats) {
    console.log('❌ Failed to analyze lite version');
    // Migrate back to original
    execSync('node scripts/migrate-dropdown.js to-original', { stdio: 'inherit' });
    return;
  }
  
  // Calculate savings
  const avgSavings = originalStats.avgFirstLoadJS - liteStats.avgFirstLoadJS;
  const totalSavings = originalStats.totalFirstLoadJS - liteStats.totalFirstLoadJS;
  const percentSavings = ((avgSavings / originalStats.avgFirstLoadJS) * 100).toFixed(1);
  
  // Display results
  console.log('\n📊 BUNDLE SIZE COMPARISON RESULTS');
  console.log('=====================================');
  console.log(`Original Average First Load JS: ${originalStats.avgFirstLoadJS} kB`);
  console.log(`Lite Average First Load JS:     ${liteStats.avgFirstLoadJS} kB`);
  console.log(`Average Savings:                ${avgSavings.toFixed(2)} kB (${percentSavings}%)`);
  console.log(`Total Savings:                  ${totalSavings.toFixed(2)} kB`);
  
  if (avgSavings > 0) {
    console.log('\n✅ RECOMMENDATION: Use lite version for better performance');
  } else {
    console.log('\n⚠️  No significant savings detected');
  }
  
  // Ask user what to do
  console.log('\n🤔 What would you like to do?');
  console.log('1. Keep lite version (recommended if savings > 5kB)');
  console.log('2. Revert to original version');
  
  // For now, let's revert to original to be safe
  console.log('\n🔄 Reverting to original version for safety...');
  execSync('node scripts/migrate-dropdown.js to-original', { stdio: 'inherit' });
  
  console.log('\n📝 To apply the lite version permanently, run:');
  console.log('   node scripts/migrate-dropdown.js to-lite');
}

// Check if this is being run directly
if (require.main === module) {
  compareVersions().catch(console.error);
}

module.exports = { analyzeBundleSize, compareVersions };
