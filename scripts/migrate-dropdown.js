#!/usr/bin/env node

/**
 * Migration script to switch between original and lite dropdown menu components
 * Usage: node scripts/migrate-dropdown.js [to-lite|to-original]
 */

const fs = require('fs');
const path = require('path');

const componentsToMigrate = [
  'components/Header/UserLoggedInButton.tsx',
  'components/common/SendAsDropdown.tsx'
];

const originalImport = 'from "../UI/dropdown-menu"';
const liteImport = 'from "../UI/dropdown-menu-lite"';
const originalImportAlt = 'from "@/components/UI/dropdown-menu"';
const liteImportAlt = 'from "@/components/UI/dropdown-menu-lite"';

function migrateToLite() {
  console.log('🔄 Migrating to lite dropdown menu...');
  
  componentsToMigrate.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      let content = fs.readFileSync(filePath, 'utf8');
      
      // Replace relative imports
      content = content.replace(originalImport, liteImport);
      // Replace absolute imports
      content = content.replace(originalImportAlt, liteImportAlt);
      
      fs.writeFileSync(filePath, content);
      console.log(`✅ Migrated ${filePath} to lite version`);
    } else {
      console.log(`⚠️  File not found: ${filePath}`);
    }
  });
  
  console.log('🎉 Migration to lite version complete!');
  console.log('📦 Expected bundle size reduction: ~15-20KB');
}

function migrateToOriginal() {
  console.log('🔄 Migrating back to original dropdown menu...');
  
  componentsToMigrate.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      let content = fs.readFileSync(filePath, 'utf8');
      
      // Replace relative imports
      content = content.replace(liteImport, originalImport);
      // Replace absolute imports  
      content = content.replace(liteImportAlt, originalImportAlt);
      
      fs.writeFileSync(filePath, content);
      console.log(`✅ Migrated ${filePath} to original version`);
    } else {
      console.log(`⚠️  File not found: ${filePath}`);
    }
  });
  
  console.log('🎉 Migration to original version complete!');
}

function showUsage() {
  console.log('Usage: node scripts/migrate-dropdown.js [to-lite|to-original]');
  console.log('');
  console.log('Commands:');
  console.log('  to-lite     - Migrate to lightweight dropdown menu (smaller bundle)');
  console.log('  to-original - Migrate back to original Radix UI dropdown menu');
  console.log('');
  console.log('Files that will be migrated:');
  componentsToMigrate.forEach(file => {
    console.log(`  - ${file}`);
  });
}

const command = process.argv[2];

switch (command) {
  case 'to-lite':
    migrateToLite();
    break;
  case 'to-original':
    migrateToOriginal();
    break;
  default:
    showUsage();
    process.exit(1);
}
