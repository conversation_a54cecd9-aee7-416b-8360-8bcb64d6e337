/**
 * Simple Network Status Hook
 * Clean and readable network connectivity monitoring
 */

'use client';

import { useState, useEffect, useCallback } from 'react';

export interface NetworkStatus {
  isOnline: boolean;
  isOffline: boolean;
  effectiveType: string | null;
}

/**
 * Simple hook for monitoring network connectivity
 * @returns NetworkStatus object with connection details and utilities
 */
export function useNetworkStatus() {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: true,
    isOffline: false,
    effectiveType: null,
  });

  /**
   * Updates network status based on current connection
   */
  const updateNetworkStatus = useCallback(() => {
    const isOnline = navigator.onLine;

    // Get connection details if available
    const connection = (navigator as unknown as {
      connection?: { effectiveType?: string }
    }).connection;

    setNetworkStatus({
      isOnline,
      isOffline: !isOnline,
      effectiveType: connection?.effectiveType || null,
    });
  }, []);

  /**
   * Tests actual connectivity by making a lightweight request
   */
  const testConnectivity = useCallback(async (): Promise<boolean> => {
    if (!navigator.onLine) {
      return false;
    }

    try {
      const response = await fetch('/favicon.png', {
        method: 'HEAD',
        cache: 'no-cache',
      });
      return response.ok;
    } catch {
      return false;
    }
  }, []);

  /**
   * Checks if current page requires network connection
   */
  const isNetworkDependentPage = useCallback((pathname?: string): boolean => {
    const currentPath = pathname || window.location.pathname;

    const networkDependentPatterns = [
      /\/dashboard/,
      /\/checkout/,
      /\/payment/,
      /\/car-tickets/,
      /\/motor-tickets/,
      /\/inquiry/,
      /\/wallet/,
      /\/login/,
    ];

    return networkDependentPatterns.some(pattern => pattern.test(currentPath));
  }, []);

  /**
   * Shows simple notification to user
   */
  const showNotification = useCallback((title: string, body: string) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        body,
        icon: '/icon512_rounded.png',
      });
    }
  }, []);

  useEffect(() => {
    // Initial status check
    updateNetworkStatus();

    // Event listeners for network changes
    const handleOnline = () => {
      updateNetworkStatus();
      showNotification('خودراکس - آنلاین', 'اتصال اینترنت برقرار شد');
    };

    const handleOffline = () => {
      updateNetworkStatus();
      showNotification('خودراکس - آفلاین', 'اتصال اینترنت قطع شده است');
    };

    // Connection change listener (for mobile networks)
    const handleConnectionChange = () => {
      updateNetworkStatus();
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Listen for connection changes if supported
    const connection = (navigator as unknown as {
      connection?: { addEventListener?: (event: string, handler: () => void) => void; removeEventListener?: (event: string, handler: () => void) => void }
    }).connection;

    if (connection?.addEventListener) {
      connection.addEventListener('change', handleConnectionChange);
    }

    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);

      if (connection?.removeEventListener) {
        connection.removeEventListener('change', handleConnectionChange);
      }
    };
  }, [updateNetworkStatus, showNotification]);

  return {
    ...networkStatus,
    testConnectivity,
    isNetworkDependentPage,
    updateNetworkStatus,
  };
}

/**
 * Network Status Context Provider Component
 */
export { useNetworkStatus as default };
