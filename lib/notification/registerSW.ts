/**
 * Service Worker registration utility
 * This module handles the registration of Firebase messaging service worker
 */

/**
 * Register Firebase messaging service worker
 * @param swPath - Path to the service worker file (default: '/firebase-messaging-sw.js')
 * @returns Promise<ServiceWorkerRegistration | null> - The registration object or null if failed
 */
export async function registerFirebaseSw(swPath = '/firebase-messaging-sw.js'): Promise<ServiceWorkerRegistration | null> {
    // Check if we're in browser environment
    if (typeof window === 'undefined') {
        console.warn('Service Worker registration attempted in non-browser environment');
        return null;
    }

    // Check if service workers are supported
    if (!('serviceWorker' in navigator)) {
        console.warn('Service Workers are not supported in this browser');
        return null;
    }

    try {
        // Register the service worker
        const registration = await navigator.serviceWorker.register(swPath);
        
        console.log('✅ Firebase messaging service worker registered successfully:', registration);
        
        // Wait for the service worker to be ready
        await navigator.serviceWorker.ready;
        
        return registration;
    } catch (error) {
        console.error('❌ Failed to register Firebase messaging service worker:', error);
        return null;
    }
}

/**
 * Unregister all service workers (useful for cleanup)
 * @returns Promise<boolean> - True if all workers were unregistered successfully
 */
export async function unregisterAllServiceWorkers(): Promise<boolean> {
    if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
        return false;
    }

    try {
        const registrations = await navigator.serviceWorker.getRegistrations();
        
        const unregisterPromises = registrations.map(registration => 
            registration.unregister()
        );
        
        await Promise.all(unregisterPromises);
        
        console.log('✅ All service workers unregistered successfully');
        return true;
    } catch (error) {
        console.error('❌ Failed to unregister service workers:', error);
        return false;
    }
}

/**
 * Check if a service worker is already registered
 * @param swPath - Path to check for
 * @returns Promise<boolean> - True if the service worker is registered
 */
export async function isServiceWorkerRegistered(swPath = '/firebase-messaging-sw.js'): Promise<boolean> {
    if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
        return false;
    }

    try {
        const registrations = await navigator.serviceWorker.getRegistrations();
        
        return registrations.some(registration => 
            registration.scope.includes(swPath) || 
            registration.active?.scriptURL.includes(swPath)
        );
    } catch (error) {
        console.error('❌ Failed to check service worker registration:', error);
        return false;
    }
}
